# Matrix AI - Song Generator Integration Guide

## Overview
The `Backend/Sing_Song.py` module implements a complete AI-powered singing system that generates lyrics and performs songs with different voices and themes.

## Core Features

### 🎵 **Song Request Processing**
The system can handle various types of song requests:

```python
# Random song requests
"sing a random song"
"sing me something"
"perform a song"

# Theme-specific requests
"sing a sad song"
"sing a happy song" 
"sing a love song"
"sing a rock song"
"sing a country song"
"sing a christmas song"

# Voice gender specifications
"sing in female voice"
"sing in male voice"
"sing with a woman's voice"

# Combined requests
"sing a sad song in female voice"
"perform a love ballad with male voice"
"sing a happy song in female voice"
```

### 🎭 **Voice Selection System**

**Male Voices:**
- `en-US-DavisNeural` - Strong, commanding
- `en-US-JasonNeural` - Powerful, intense
- `en-US-SteffanNeural` - Deep, authoritative
- `en-GB-RyanNeural` - British, sophisticated
- `en-AU-WilliamNeural` - Australian, rugged

**Female Voices:**
- `en-US-JennyNeural` - Clear, expressive
- `en-US-AriaNeural` - Melodic, smooth
- `en-US-SaraNeural` - Warm, friendly
- `en-GB-SoniaNeural` - British, elegant
- `en-AU-NatashaNeural` - Australian, vibrant

### 🎼 **Supported Themes**
- **Sad**: Melancholy, sorrowful, tragic songs
- **Happy**: Joyful, cheerful, upbeat songs
- **Love**: Romantic, passionate, heart songs
- **Rock**: Heavy, aggressive, powerful songs
- **Pop**: Catchy, mainstream, radio-friendly songs
- **Country**: Folk, rural, western, acoustic songs
- **Rap**: Hip hop, rhythm, beat, rhyme songs
- **Ballad**: Slow, emotional, tender songs
- **Christmas**: Holiday, winter, festive songs
- **Birthday**: Celebration, party, special occasion songs
- **Random**: AI chooses theme automatically

## Integration with Main Application

### 1. **Import the Module**
Add to your main.py imports:
```python
from Backend.Sing_Song import process_song_request, is_song_request
```

### 2. **Add to Command Processing**
In your main command processing loop, add:
```python
# Check if user wants a song
if is_song_request(user_input):
    success = process_song_request(user_input)
    if success:
        SetAssistantStatus("Song performance complete")
    else:
        SetAssistantStatus("Unable to perform song")
    continue
```

### 3. **Alternative Integration**
For more control, use the main function directly:
```python
from Backend.Sing_Song import generate_and_sing_song

# In your command processing
if "sing" in user_input.lower():
    SetAssistantStatus("Preparing to sing...")
    success = generate_and_sing_song(user_input)
    if success:
        SetAssistantStatus("Song complete")
    else:
        SetAssistantStatus("Song failed")
```

## Technical Specifications

### 🎛️ **Singing Voice Settings**
```python
SINGING_VOICE_SETTINGS = {
    "pitch": "+5Hz",            # Slightly higher for singing
    "rate": "-30%",             # Slower for musical rhythm
    "volume": "+15%",           # Louder for performance
}
```

### 🎵 **Musical Enhancements**
- **Breathing Pauses**: Added at natural break points
- **Musical Rhythm**: Slower speech rate for song-like delivery
- **Verse Separation**: Extended pauses between song sections
- **Emphasis**: Strategic pauses for dramatic effect
- **Volume**: Increased for performance quality

### 🔄 **Error Handling**
1. **Voice Fallback**: If primary voice fails, tries alternative voices
2. **Speaking Fallback**: If singing fails, speaks lyrics instead
3. **Graceful Degradation**: Always provides some form of output
4. **Clean Cleanup**: Removes temporary audio files

## User Experience Flow

### 1. **Request Processing**
```
User: "sing a sad song in female voice"
↓
System: "Generating lyrics for your song..."
```

### 2. **Lyrics Generation**
- Uses existing ChatGPT integration for AI-generated lyrics
- Falls back to built-in lyrics if AI unavailable
- Creates structured songs with verses and chorus

### 3. **Song Announcement**
```
System: "Now singing: Digital Tears in female voice"
```

### 4. **Performance**
- Plays generated song with musical delivery
- Enhanced audio quality for singing
- Natural pauses and rhythm

### 5. **Completion**
```
System: "Song performance complete"
```

## Example Usage Scenarios

### **Scenario 1: Basic Song Request**
```
User: "sing me a song"
System: "Generating lyrics for your song..."
System: "Now singing: Electric Joy"
[Performs upbeat song with neutral voice]
```

### **Scenario 2: Specific Theme and Voice**
```
User: "sing a love song in female voice"
System: "Generating lyrics for your song..."
System: "Now singing: Binary Heart in female voice"
[Performs romantic song with female voice]
```

### **Scenario 3: Error Handling**
```
User: "sing a rock song"
System: "Generating lyrics for your song..."
System: "Now singing: Digital Thunder"
[If singing fails]
System: "Unable to sing the song. Let me speak the lyrics instead."
[Speaks lyrics with AI voice]
```

## File Structure

### **Generated Files**
- `Data/song.mp3` - Temporary audio file (auto-deleted)

### **Dependencies**
- `edge_tts` - Text-to-speech generation
- `pygame` - Audio playback
- `Backend.ChatGPT` - AI lyrics generation (optional)
- `Backend.TextToSpeech` - Announcements and fallback

## Configuration Options

### **Voice Customization**
Modify voice arrays in `Sing_Song.py`:
```python
MALE_VOICES = ["en-US-DavisNeural", ...]  # Add/remove male voices
FEMALE_VOICES = ["en-US-JennyNeural", ...] # Add/remove female voices
```

### **Singing Parameters**
Adjust musical delivery:
```python
SINGING_VOICE_SETTINGS = {
    "pitch": "+5Hz",     # Adjust pitch for singing
    "rate": "-30%",      # Adjust speed for rhythm
    "volume": "+15%",    # Adjust volume for performance
}
```

### **Theme Keywords**
Add new themes or keywords in `extract_song_theme_from_input()`:
```python
theme_patterns = {
    "jazz": r"\b(jazz|swing|blues|smooth)\b",
    "electronic": r"\b(electronic|techno|edm|synth)\b",
    # Add more themes...
}
```

## Performance Considerations

### **Memory Usage**
- Temporary audio files are automatically cleaned up
- Minimal memory footprint during operation
- Efficient voice selection and caching

### **Response Time**
- Lyrics generation: 2-5 seconds (depends on AI response)
- Audio generation: 3-8 seconds (depends on song length)
- Total time: 5-15 seconds for complete song performance

### **Audio Quality**
- 22050Hz frequency for clear singing voice
- Stereo channels for immersive experience
- Optimized buffer for smooth playback
- Enhanced volume for performance quality

This singing system provides a complete musical experience while maintaining the Matrix AI's heavy, dangerous persona through voice selection and delivery style.
