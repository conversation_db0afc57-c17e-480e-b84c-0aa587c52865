import asyncio
from random import randint
from PIL import Image
import requests
from dotenv import get_key
import os
from time import sleep

def open_images(prompt):
    folder_path = r"Data"
    prompt = prompt.replace(" ", "_")
    files = [f"{prompt}{i}.jpg" for i in range(1, 5)]

    for jpg_file in files:
        image_path = os.path.join(folder_path, jpg_file)
        try:
            img = Image.open(image_path)
            print(f"Opening image: {image_path}")
            img.show()
            sleep(1)
        except IOError:
            print(f"Unable to open {image_path}")

# Ensure the output folder exists.
if not os.path.exists("Data"):
    os.makedirs("Data")

API_URL = "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0"
headers = {"Authorization": f"Bearer {get_key('.env', 'HuggingFaceAPIKey')}"}

async def query(payload):
    # Call requests.post in a separate thread to avoid blocking
    response = await asyncio.to_thread(requests.post, API_URL, headers=headers, json=payload)
    if response.status_code != 200:
        print("API error:", response.status_code, response.text)
    return response.content

async def generate_images(prompt: str):
    tasks = []
    for _ in range(4):
        # Removed extra spaces around "=" in the seed parameter.
        payload = {
            "inputs": f"{prompt}, quality=4k, sharpness=maximum, Ultra High details, high resolution, seed={randint(0, 1000000)}",
        }
        task = asyncio.create_task(query(payload))
        tasks.append(task)

    image_bytes_list = await asyncio.gather(*tasks)

    for i, image_bytes in enumerate(image_bytes_list):
        # Use os.path.join for portability.
        file_path = os.path.join("Data", f"{prompt.replace(' ', '_')}{i + 1}.jpg")
        with open(file_path, "wb") as f:
            f.write(image_bytes)

def generate_images_sync(prompt: str):
    asyncio.run(generate_images(prompt))
    open_images(prompt)

while True:
    try:
        # FIX: Use parentheses ( ) with open, not curly braces { }.
        with open(r"Frontend\Files\ImageGeneration.data", "r") as f:
            data = f.read().strip()

        # Expecting the file to contain two comma-separated values.
        prompt, status = data.split(",")
        if status.strip() == "True":
            print("Generating Images...")
            generate_images_sync(prompt=prompt)
            # FIX: Use consistent folder casing. (Changed "frontend" to "Frontend")
            with open(r"Frontend\Files\ImageGeneration.data", "w") as f:
                f.write("False,False")
            break
        else:
            sleep(1)

    # except Exception as e:
    #     print("Error:", e)

    except:
        pass
