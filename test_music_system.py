#!/usr/bin/env python3
"""
Test script for the Matrix AI Enhanced Music System
Tests both SING and PLAY modes with the OpenRouter API
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Backend.Sing_Song import (
    is_sing_request, 
    is_play_request, 
    process_song_request,
    extract_song_info_for_youtube
)

def test_command_detection():
    """Test the command detection system"""
    print("🎵 Testing Command Detection System")
    print("=" * 50)
    
    # Test SING commands
    sing_commands = [
        "sing a sad song",
        "sing me something happy",
        "perform a love ballad",
        "can you sing",
        "serenade me",
        "compose a rock song",
        "generate a song about love"
    ]
    
    print("\n🎤 SING Command Detection:")
    for cmd in sing_commands:
        is_sing = is_sing_request(cmd)
        is_play = is_play_request(cmd)
        print(f"  '{cmd}' → SING: {is_sing}, PLAY: {is_play}")
    
    # Test PLAY commands
    play_commands = [
        "play bohemian rhapsody",
        "play some taylor swift",
        "stream rock music",
        "listen to jazz",
        "play a song by metallica",
        "put on some music"
    ]
    
    print("\n🎧 PLAY Command Detection:")
    for cmd in play_commands:
        is_sing = is_sing_request(cmd)
        is_play = is_play_request(cmd)
        print(f"  '{cmd}' → SING: {is_sing}, PLAY: {is_play}")

def test_youtube_extraction():
    """Test YouTube search query extraction"""
    print("\n🔍 Testing YouTube Search Extraction")
    print("=" * 50)
    
    test_queries = [
        "play bohemian rhapsody",
        "play taylor swift",
        "stream some jazz music",
        "listen to metallica",
        "play rock music",
        "put on some classical music"
    ]
    
    for query in test_queries:
        extracted = extract_song_info_for_youtube(query)
        print(f"  '{query}' → Search: '{extracted}'")

def test_api_connection():
    """Test OpenRouter API connection"""
    print("\n🤖 Testing OpenRouter API Connection")
    print("=" * 50)
    
    try:
        from Backend.Lyric_Writer import generate_lyrics
        
        print("  Generating test lyrics for 'happy' theme...")
        result = generate_lyrics("happy", "neutral")
        
        if result["source"] == "openrouter":
            print("  ✅ OpenRouter API working successfully!")
            print(f"  📝 Generated title: '{result['title']}'")
            print(f"  🎵 Lyrics preview: {result['lyrics'][:100]}...")
        else:
            print("  ⚠️  Using fallback lyrics (API may be unavailable)")
            
    except Exception as e:
        print(f"  ❌ API test failed: {e}")

def interactive_test():
    """Interactive test mode"""
    print("\n🎮 Interactive Test Mode")
    print("=" * 50)
    print("Enter commands to test the music system:")
    print("Examples:")
    print("  - 'sing a sad song'")
    print("  - 'play bohemian rhapsody'")
    print("  - 'quit' to exit")
    print()
    
    while True:
        try:
            user_input = input("🎵 Enter command: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
            
            print(f"\n📝 Processing: '{user_input}'")
            
            # Test detection
            is_sing = is_sing_request(user_input)
            is_play = is_play_request(user_input)
            
            print(f"🔍 Detection → SING: {is_sing}, PLAY: {is_play}")
            
            if is_sing:
                print("🎤 This would generate and perform AI lyrics")
                print("   (Skipping actual performance in test mode)")
            elif is_play:
                search_query = extract_song_info_for_youtube(user_input)
                print(f"🎧 This would open YouTube search for: '{search_query}'")
                print("   (Skipping browser opening in test mode)")
            else:
                print("❓ Not recognized as a music command")
            
            print("-" * 30)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🤖 Matrix AI Enhanced Music System Test")
    print("🎵 SING Mode: AI Lyrics Generation with Meta Llama 3.1 8B")
    print("🎧 PLAY Mode: YouTube Music Streaming")
    print("=" * 60)
    
    # Run all tests
    test_command_detection()
    test_youtube_extraction()
    test_api_connection()
    
    # Ask for interactive test
    print("\n" + "=" * 60)
    response = input("🎮 Run interactive test? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        interactive_test()
    
    print("\n✅ Test completed!")
    print("🎯 The music system is ready for use!")

if __name__ == "__main__":
    main()
