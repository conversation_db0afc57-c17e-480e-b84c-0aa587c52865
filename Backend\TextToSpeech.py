import pygame
import random
import asyncio
import edge_tts
import os
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")
AssistantVoice = env_vars.get("AssistantVoice", "en-US-SteffanNeural")  # Deep AI-like voice

# Path for audio file
AUDIO_FILE_PATH = r"Data\speech.mp3"

async def text_to_audio_file(text: str) -> None:
    """Generates an audio file from text using edge_tts."""
    if os.path.exists(AUDIO_FILE_PATH):
        os.remove(AUDIO_FILE_PATH)
    
    try:
        communicate = edge_tts.Communicate(text, AssistantVoice, pitch='-10Hz', rate='-10%')  # Deeper, slower
        await communicate.save(AUDIO_FILE_PATH)
    except Exception as e:
        print(f"Error with voice '{AssistantVoice}': {e}. Switching to fallback voice.")
        communicate = edge_tts.Communicate(text, "en-US-SteffanNeural", pitch='-10Hz', rate='-10%')
        await communicate.save(AUDIO_FILE_PATH)

async def run_tts(text: str):
    """Runs text-to-speech conversion asynchronously."""
    await text_to_audio_file(text)

def tts(text: str, func=lambda r=None: True):
    """Handles text-to-speech and plays the generated audio file."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(run_tts(text))

    pygame.mixer.init()
    pygame.mixer.music.load(AUDIO_FILE_PATH)
    pygame.mixer.music.play()

    clock = pygame.time.Clock()
    while pygame.mixer.music.get_busy():
        if not func():
            break
        clock.tick(10)
    
    pygame.mixer.music.stop()
    pygame.mixer.quit()

def text_to_speech(text: str, func=lambda r=None: True):
    """Splits text if too long and provides responses accordingly."""
    responses = [
        "The rest of the result has been printed to the chat screen, kindly check it out sir.",
        "The rest of the text is now on the chat screen, sir, please check it.",
        "You can see the rest of the text on the chat screen, sir.",
        "The remaining part of the text is now on the chat screen, sir.",
        "Sir, you'll find more text on the chat screen for you to see.",
        "The rest of the answer is now on the chat screen, sir.",
        "Sir, please look at the chat screen, the rest of the answer is there.",
        "You'll find the complete answer on the chat screen, sir.",
        "The next part of the text is on the chat screen, sir.",
        "Sir, please check the chat screen for more information.",
        "There's more text on the chat screen for you, sir.",
        "Sir, take a look at the chat screen for additional text.",
        "You'll find more to read on the chat screen, sir.",
        "Sir, check the chat screen for the rest of the text.",
        "The chat screen has the rest of the text, sir.",
        "There's more to see on the chat screen, sir, please look.",
        "Sir, the chat screen holds the continuation of the text.",
        "You'll find the complete answer on the chat screen, kindly check it out sir.",
        "Please review the chat screen for the rest of the text, sir.",
        "Sir, look at the chat screen for the complete answer."
    ]
    
    if len(text.split(".")) > 4 and len(text) > 250:
        tts(" ".join(text.split(".")[:2]) + ". " + random.choice(responses), func)
    else:
        tts(text, func)

if __name__ == "__main__":
    while True:
        text_to_speech(input("Enter the Text: "))