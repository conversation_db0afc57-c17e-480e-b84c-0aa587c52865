#!/usr/bin/env python3
"""
Comprehensive test for the Enhanced Matrix AI Singing System
This will actually generate and perform songs with theme-specific vocals
"""

import sys
import os
import asyncio

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Backend.Sing_Song import (
    generate_and_sing_song,
    is_sing_request,
    get_theme_voice_settings,
    THEME_VOICE_SETTINGS
)

def test_theme_detection():
    """Test theme detection for various commands"""
    print("🔍 Testing Theme Detection")
    print("=" * 50)
    
    test_commands = [
        "sing a rock song",
        "sing a love ballad", 
        "sing a sad song",
        "sing a happy song",
        "sing a jazz song",
        "sing a country song",
        "sing a rap song",
        "sing a classical song"
    ]
    
    for cmd in test_commands:
        is_sing = is_sing_request(cmd)
        print(f"✅ '{cmd}' → Detected as SING: {is_sing}")
    
    print()

def display_voice_settings_summary():
    """Display a summary of voice settings"""
    print("🎭 Voice Settings Summary")
    print("=" * 50)
    
    themes_to_show = ["rock", "love", "sad", "happy", "jazz", "rap"]
    
    for theme in themes_to_show:
        settings = get_theme_voice_settings(theme)
        print(f"🎵 {theme.upper()}: {settings['pitch']} pitch, {settings['rate']} rate, {settings['volume']} volume")
    
    print()

def test_single_theme_singing(theme="happy"):
    """Test singing with a specific theme"""
    print(f"🎤 Testing {theme.upper()} Theme Singing")
    print("=" * 50)
    
    # Get voice settings for this theme
    settings = get_theme_voice_settings(theme)
    print(f"Voice Settings: {settings['pitch']} pitch, {settings['rate']} rate, {settings['volume']} volume")
    print(f"Style: {settings['style']}, Emphasis: {settings['emphasis']}")
    print()
    
    # Create the singing command
    command = f"sing a {theme} song"
    print(f"Command: '{command}'")
    print("🎵 Starting song generation and performance...")
    print()
    
    try:
        # This will actually generate lyrics and sing the song
        success = generate_and_sing_song(command)
        
        if success:
            print("✅ Song performance completed successfully!")
            print(f"🎭 The AI sang with {theme} vocal style:")
            print(f"   - Pitch: {settings['pitch']}")
            print(f"   - Rate: {settings['rate']}")
            print(f"   - Volume: {settings['volume']}")
            print(f"   - Style: {settings['style']} delivery")
        else:
            print("❌ Song performance failed")
            
    except Exception as e:
        print(f"❌ Error during singing test: {e}")
    
    print()

def test_multiple_themes():
    """Test singing with multiple different themes"""
    print("🎭 Testing Multiple Theme Performances")
    print("=" * 50)
    
    themes_to_test = ["rock", "love", "jazz"]
    
    for i, theme in enumerate(themes_to_test, 1):
        print(f"\n🎵 Test {i}/3: {theme.upper()} Theme")
        print("-" * 30)
        
        settings = get_theme_voice_settings(theme)
        print(f"Settings: {settings['pitch']} pitch, {settings['rate']} rate, {settings['style']} style")
        
        command = f"sing a {theme} song"
        print(f"Command: '{command}'")
        
        try:
            print("🎤 Generating and performing...")
            success = generate_and_sing_song(command)
            
            if success:
                print(f"✅ {theme.upper()} song completed!")
            else:
                print(f"❌ {theme.upper()} song failed")
                
        except Exception as e:
            print(f"❌ Error with {theme} song: {e}")
        
        if i < len(themes_to_test):
            print("\n⏳ Waiting 3 seconds before next test...")
            import time
            time.sleep(3)

def interactive_singing_test():
    """Interactive test where user can request specific songs"""
    print("🎮 Interactive Enhanced Singing Test")
    print("=" * 50)
    print("Test the enhanced singing system with real performances!")
    print()
    print("Available themes:")
    themes = list(THEME_VOICE_SETTINGS.keys())
    for i, theme in enumerate(themes, 1):
        print(f"  {i:2d}. {theme}")
    print()
    print("Commands:")
    print("  - 'sing [theme] song' (e.g., 'sing rock song')")
    print("  - 'test [theme]' to see settings")
    print("  - 'quit' to exit")
    print()
    
    while True:
        try:
            user_input = input("🎵 Enter command: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
            
            if user_input.startswith('test '):
                theme = user_input[5:].strip()
                if theme in THEME_VOICE_SETTINGS:
                    settings = get_theme_voice_settings(theme)
                    print(f"\n🎭 {theme.upper()} Theme Settings:")
                    print(f"   Pitch: {settings['pitch']}")
                    print(f"   Rate: {settings['rate']}")
                    print(f"   Volume: {settings['volume']}")
                    print(f"   Style: {settings['style']}")
                    print(f"   Emphasis: {settings['emphasis']}")
                else:
                    print(f"❓ Unknown theme: {theme}")
                    
            elif is_sing_request(user_input):
                print(f"\n🎤 Processing: '{user_input}'")
                print("🎵 Generating lyrics and performing song...")
                
                try:
                    success = generate_and_sing_song(user_input)
                    if success:
                        print("✅ Song performance completed!")
                    else:
                        print("❌ Song performance failed")
                except Exception as e:
                    print(f"❌ Error: {e}")
            else:
                print("❓ Try 'sing [theme] song' or 'test [theme]'")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🤖 Matrix AI Enhanced Singing System - LIVE TEST")
    print("🎵 Theme-Specific Vocal Tones & Musical Delivery")
    print("🎭 Authentic Singing Performance Test")
    print("=" * 60)
    
    # Test theme detection
    test_theme_detection()
    
    # Show voice settings summary
    display_voice_settings_summary()
    
    # Ask which test to run
    print("Choose test type:")
    print("1. Quick single theme test (happy song)")
    print("2. Multiple themes test (rock, love, jazz)")
    print("3. Interactive test (choose your own)")
    print("4. Skip to interactive mode")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            test_single_theme_singing("happy")
            
        elif choice == "2":
            test_multiple_themes()
            
        elif choice == "3":
            test_single_theme_singing("rock")
            
        elif choice == "4":
            pass  # Skip to interactive
            
        else:
            print("Invalid choice, proceeding to interactive test...")
        
        # Ask for interactive test
        print("\n" + "=" * 60)
        response = input("🎮 Run interactive singing test? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            interactive_singing_test()
        
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user")
    
    print("\n✅ Enhanced singing system test completed!")
    print("🎯 The system is ready for authentic musical performances!")

if __name__ == "__main__":
    main()
