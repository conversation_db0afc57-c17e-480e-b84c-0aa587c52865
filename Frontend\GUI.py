from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QTextEdit, QStackedWidget, QWidget, QGridLayout, QVBoxLayout, QPushButton, QFrame, QLabel, QSizePolicy, QHBoxLayout
from PyQt5.QtGui import QIcon, QPainter, QMovie, QColor, QTextCharFormat, QFont, QPixmap, QTextBlockFormat, QPen, QBrush, QPainterPath
from PyQt5.QtCore import Qt, QSize, QTimer, QPointF, QRectF, pyqtSignal
import math
import random
from dotenv import dotenv_values
import sys, os

# Environment and file path setup
env_vars = dotenv_values(".env")
Assistantname = env_vars.get("Assistantname", "Assistant")
current_dir = os.getcwd()
old_chat_message = ""
TempDirectoryPath = os.path.join(current_dir, "Frontend", "Files")
GraphicsDirectoryPath = os.path.join(current_dir, "Frontend", "Graphics")

# --- Voice Assistant Animation ---
class VoiceAssistantAnimation(QWidget):
    """
    A circular animation similar to Siri with lines that respond to sound.
    The animation changes based on the assistant's state (listening, speaking, idle).
    """

    def __init__(self, parent=None, size=300):
        super().__init__(parent)
        self.setMinimumSize(size, size)
        self.setMaximumSize(size, size)

        # Animation parameters
        self.num_lines = 40  # Reduced number of lines for smaller size (was 60)
        self.base_radius = size // 2 - 15  # Base radius of the circle (adjusted for smaller size)
        self.min_line_length = 5  # Minimum line length (reduced from 10)
        self.max_line_length = 25  # Maximum line length when active (reduced from 40)
        self.line_width = 2  # Width of each line (reduced from 3)

        # Animation state
        self.mode = "idle"  # Can be "idle", "listening", or "speaking"
        self.amplitudes = [self.min_line_length] * self.num_lines  # Current amplitude of each line
        self.target_amplitudes = [self.min_line_length] * self.num_lines  # Target amplitude for smooth animation
        self.animation_speed = 0.25  # Speed of amplitude change (0-1) - increased for smoother animation
        self.animation_counter = 0  # Counter for animation timing

        # Colors
        self.idle_color = QColor(80, 140, 240, 200)  # Soft blue when idle
        self.listening_color = QColor(60, 220, 120, 200)  # Soft green when listening
        self.speaking_color = QColor(240, 100, 100, 200)  # Soft red when speaking
        self.current_color = self.idle_color

        # Start the animation timer
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(16)  # ~60 FPS

        # Timer to check assistant status
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_assistant_status)
        self.status_timer.start(100)  # Check status every 100ms

    def check_assistant_status(self):
        """Check the assistant's status from the Status.data file and update animation mode."""
        try:
            status = GetAssistantStatus().lower()

            if "listening" in status:
                self.set_mode("listening")
            elif any(keyword in status for keyword in ["speaking", "generating", "translating"]):
                self.set_mode("speaking")
            else:
                self.set_mode("idle")
        except Exception as e:
            print(f"Error checking assistant status: {e}")

    def set_mode(self, mode):
        """Set the animation mode and update colors."""
        if self.mode != mode:
            self.mode = mode
            if mode == "idle":
                self.current_color = self.idle_color
            elif mode == "listening":
                self.current_color = self.listening_color
            elif mode == "speaking":
                self.current_color = self.speaking_color

    def update_animation(self):
        """Update the animation state for the next frame."""
        # Increment animation counter
        self.animation_counter += 1

        if self.mode == "idle":
            # Gentle pulsing in idle mode
            base_amplitude = self.min_line_length + (self.max_line_length - self.min_line_length) * 0.3
            pulse_factor = 0.1 * math.sin(0.1 * self.animation_counter)
            for i in range(self.num_lines):
                self.target_amplitudes[i] = base_amplitude + pulse_factor * self.max_line_length

        elif self.mode == "listening":
            # Responsive wave pattern when listening
            for i in range(self.num_lines):
                # Create a wave pattern that moves around the circle
                angle = 2 * math.pi * i / self.num_lines
                time_factor = 0.05 * self.animation_counter
                wave = math.sin(angle * 3 + time_factor) * 0.5 + 0.5
                self.target_amplitudes[i] = self.min_line_length + wave * (self.max_line_length - self.min_line_length)

        elif self.mode == "speaking":
            # More energetic, random movement when speaking
            for i in range(self.num_lines):
                # Random heights with some coherence between adjacent lines
                if random.random() < 0.1:  # Occasionally update target
                    rand_val = random.random()
                    self.target_amplitudes[i] = self.min_line_length + rand_val * (self.max_line_length - self.min_line_length)

        # Smoothly animate current amplitudes toward target amplitudes
        for i in range(self.num_lines):
            self.amplitudes[i] += (self.target_amplitudes[i] - self.amplitudes[i]) * self.animation_speed

        # Request a repaint
        self.update()

    def paintEvent(self, _):
        """Paint the animation."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Calculate center point
        center_x = self.width() // 2
        center_y = self.height() // 2

        # Draw the lines
        for i in range(self.num_lines):
            # Calculate angle for this line
            angle = 2 * math.pi * i / self.num_lines

            # Calculate start and end points
            inner_radius = self.base_radius - self.amplitudes[i]
            outer_radius = self.base_radius + self.amplitudes[i]

            start_x = center_x + inner_radius * math.cos(angle)
            start_y = center_y + inner_radius * math.sin(angle)

            end_x = center_x + outer_radius * math.cos(angle)
            end_y = center_y + outer_radius * math.sin(angle)

            # Set up the pen
            pen = QPen(self.current_color)
            pen.setWidth(self.line_width)
            pen.setCapStyle(Qt.RoundCap)
            painter.setPen(pen)

            # Draw the line
            painter.drawLine(int(start_x), int(start_y), int(end_x), int(end_y))

    def sizeHint(self):
        """Return the preferred size of the widget."""
        return QSize(300, 300)

# --- Utility Functions ---
def AnswerModifier(Answer):
    lines = Answer.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    return '\n'.join(non_empty_lines)

def QueryModifier(Query):
    new_query = Query.lower().strip()
    query_words = new_query.split()
    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "what is", "where's", "how's"]
    if any(word + " " in new_query for word in question_words):
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1]
        else:
            new_query += "?"
    else:
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."
    return new_query.capitalize()

def SetMicrophoneStatus(command):
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "w", encoding='utf-8') as file:
            file.write(command)
    except Exception as e:
        print("Error writing Mic.data:", e)

def GetMicrophoneStatus():
    try:
        with open(os.path.join(TempDirectoryPath, "Mic.data"), "r", encoding='utf-8') as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Mic.data:", e)
        return ""

# def SetAssistantStatus(Status):
#     try:
#         with open(os.path.join(TempDirectoryPath, "Status.data"), "w", encoding='utf-8') as file:
#             file.write(Status)
#     except Exception as e:
#         print("Error writing Status.data:", e)

# def GetAssistantStatus():
#     try:
#         with open(os.path.join(TempDirectoryPath, "Status.data"), "r", encoding='utf-8') as file:
#             return file.read().strip()
#     except Exception as e:
#         print("Error reading Status.data:", e)
#         return ""

def SetAssistantStatus(Status):
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    # Ensure directory exists
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    try:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(Status)
    except Exception as e:
        print("Error writing Status.data:", e)

def GetAssistantStatus():
    file_path = os.path.join(TempDirectoryPath, "Status.data")
    # Ensure directory exists
    if not os.path.exists(TempDirectoryPath):
        os.makedirs(TempDirectoryPath)
    # If file doesn't exist, initialize it with an empty string
    if not os.path.exists(file_path):
        SetAssistantStatus("")
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return file.read().strip()
    except Exception as e:
        print("Error reading Status.data:", e)
        return ""


# Update mic status functions to write to Mic.data
def MicButtonInitialed():
    SetMicrophoneStatus("True")

def MicButtonClosed():
    SetMicrophoneStatus("False")

def GraphicsPath(Filename):
    return os.path.join(GraphicsDirectoryPath, Filename)

def TempPath(Filename):
    return os.path.join(TempDirectoryPath, Filename)

def ShowTextToScreen(Text):
    try:
        with open(os.path.join(TempDirectoryPath, "Responses.data"), "w", encoding='utf-8') as file:
            file.write(Text)
    except Exception as e:
        print("Error writing Responses.data:", e)

# --- ChatSection: Handles chat text, GIF display, and periodic updates ---
class ChatSection(QWidget):
    def __init__(self):
        super(ChatSection, self).__init__()
        layout = QVBoxLayout(self)
        layout.setContentsMargins(-10, 80, 40, 100)  # Increased top margin from 40 to 80
        layout.setSpacing(-100)

        self.chat_text_edit = QTextEdit()
        self.chat_text_edit.setReadOnly(True)
        self.chat_text_edit.setTextInteractionFlags(Qt.NoTextInteraction)
        self.chat_text_edit.setFrameStyle(QFrame.NoFrame)
        layout.addWidget(self.chat_text_edit)

        self.setStyleSheet("background-color: black;")
        layout.setSizeConstraint(QVBoxLayout.SetDefaultConstraint)
        layout.setStretch(1, 1)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        text_color = QColor(Qt.blue)
        text_format = QTextCharFormat()
        text_format.setForeground(text_color)
        self.chat_text_edit.setCurrentCharFormat(text_format)

        # Create voice assistant animation widget
        self.animation_widget = VoiceAssistantAnimation(size=200)  # Reduced from 300 to 200
        self.animation_widget.setStyleSheet("border: none; background-color: transparent; margin-top: 100px;")
        layout.addWidget(self.animation_widget, alignment=Qt.AlignRight | Qt.AlignBottom)

        self.label = QLabel("")
        self.label.setStyleSheet("color: white; font-size:16px; margin-right: 120px; border: none; margin-top: 10px;")
        self.label.setAlignment(Qt.AlignRight)
        layout.addWidget(self.label)
        layout.setSpacing(5)  # Increased from -10 to 5 for better spacing

        font = QFont()
        font.setPointSize(13)
        self.chat_text_edit.setFont(font)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.loadMessages)
        self.timer.timeout.connect(self.SpeechRecogText)
        self.timer.start(100)  # Adjust interval if needed

        self.chat_text_edit.viewport().installEventFilter(self)

    def loadMessages(self):
        global old_chat_message
        try:
            with open(TempPath("Responses.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            if messages and messages != old_chat_message:
                self.addMessage(messages, color="White")
                old_chat_message = messages
        except Exception as e:
            print("Error loading messages:", e)

    def SpeechRecogText(self):
        try:
            with open(TempPath("Status.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            self.label.setText(messages)
        except Exception as e:
            print("Error updating speech recognition text:", e)

    def addMessage(self, message, color):
        cursor = self.chat_text_edit.textCursor()
        fmt = QTextCharFormat()
        blockFmt = QTextBlockFormat()
        blockFmt.setTopMargin(10)
        blockFmt.setLeftMargin(10)
        fmt.setForeground(QColor(color))
        cursor.setCharFormat(fmt)
        cursor.setBlockFormat(blockFmt)
        cursor.insertText(message + "\n")
        self.chat_text_edit.setTextCursor(cursor)

# --- InitialScreen: The main screen with GIF, mic icon, and status display ---
class InitialScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)  # Removed top margin here (will be set later)

        # Create voice assistant animation widget
        animation_widget = VoiceAssistantAnimation(size=250)  # Reduced from 400 to 250
        animation_widget.setStyleSheet("border: none; background-color: transparent;")
        animation_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.icon_label = QLabel()
        pixmap = QPixmap(GraphicsPath('Mic_on.png'))
        if pixmap.isNull():
            print("Warning: 'Mic_on.png' not found or is a null pixmap")
        new_pixmap = pixmap.scaled(60, 60)
        self.icon_label.setPixmap(new_pixmap)
        self.icon_label.setFixedSize(150, 150)
        self.icon_label.setAlignment(Qt.AlignCenter)

        # Set initial mic state and toggle functionality:
        self.toggled = False  # False means mic is ON initially (ready to listen)
        self.toggle_icon = self.__toggle_icon_impl
        self.icon_label.mousePressEvent = self.toggle_icon
        # Make sure the initial icon matches the initial state (mic ON)
        self.load_icon(GraphicsPath('Mic_on.png'))
        print("🎤 GUI: Microphone button initialized as ACTIVE")

        self.label = QLabel("")
        self.label.setStyleSheet("color: white; font-size:16px; margin-bottom: 0;")

        content_layout.addWidget(animation_widget, alignment=Qt.AlignCenter)
        content_layout.addSpacing(20)  # Add space between animation and status label
        content_layout.addWidget(self.label, alignment=Qt.AlignCenter)
        content_layout.addSpacing(30)  # Add space between status label and mic button
        content_layout.addWidget(self.icon_label, alignment=Qt.AlignCenter)
        content_layout.setContentsMargins(0, 120, 0, 100)  # Increased top margin from 50px to 120px
        self.setLayout(content_layout)
        self.setFixedHeight(screen_height)
        self.setFixedWidth(screen_width)
        self.setStyleSheet("background-color: black;")

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.SpeechRecogText)
        self.timer.start(100)

    def SpeechRecogText(self):
        try:
            with open(TempPath("Status.data"), "r", encoding='utf-8') as file:
                messages = file.read()
            self.label.setText(messages)
        except Exception as e:
            print("Error in InitialScreen SpeechRecogText:", e)

    def __toggle_icon_impl(self, _=None):
        if not self.toggled:
            self.load_icon(GraphicsPath('Mic_on.png'))
            MicButtonInitialed()
            print("Microphone activated")
            # Add a small delay to allow the UI to update before starting speech recognition
            import time
            time.sleep(0.1)
        else:
            self.load_icon(GraphicsPath('Mic_off.png'))
            MicButtonClosed()
            print("Microphone deactivated")
        self.toggled = not self.toggled

    def load_icon(self, path, width=60, height=60):
        pixmap = QPixmap(path)
        if pixmap.isNull():
            print("Warning: Icon at", path, "is a null pixmap")
        new_pixmap = pixmap.scaled(width, height)
        self.icon_label.setPixmap(new_pixmap)

# --- MessageScreen: Displays the chat area ---
class MessageScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()
        layout = QVBoxLayout()
        dummy_label = QLabel("")
        layout.addWidget(dummy_label)
        chat_section = ChatSection()
        layout.addWidget(chat_section)
        self.setLayout(layout)
        self.setStyleSheet("background-color: black;")
        self.setFixedHeight(screen_height)
        self.setFixedWidth(screen_width)

# --- CustomTopBar: The top bar with buttons for home, chat, minimize, maximize, close ---
class CustomTopBar(QWidget):
    def __init__(self, parent, stacked_widget):
        super().__init__(parent)
        self.stacked_widget = stacked_widget
        self.current_screen = None
        self.initUI()

    def initUI(self):
        # Increase width so buttons are visible; set fixed height too.
        self.setFixedWidth(1420)
        self.setFixedHeight(60)
        layout = QHBoxLayout(self)
        layout.setAlignment(Qt.AlignRight)

        home_button = QPushButton()
        home_icon = QIcon(GraphicsPath("Home.png"))
        home_button.setIcon(home_icon)
        home_button.setText("  Home")
        home_button.setStyleSheet("height: 40px; background-color: white; color: black;")

        message_button = QPushButton()
        message_icon = QIcon(GraphicsPath("Chats.png"))
        message_button.setIcon(message_icon)
        message_button.setText("  Chat")
        message_button.setStyleSheet("height: 40px; background-color: white; color: black;")

        minimize_button = QPushButton()
        minimize_icon = QIcon(GraphicsPath('Minimize2.png'))
        minimize_button.setIcon(minimize_icon)
        minimize_button.setStyleSheet("background-color: white;")
        minimize_button.clicked.connect(self.minimizeWindow)

        self.maximize_button = QPushButton()
        self.maximize_icon = QIcon(GraphicsPath('Maximize.png'))
        self.restore_icon = QIcon(GraphicsPath('Minimize.png'))
        self.maximize_button.setIcon(self.maximize_icon)
        self.maximize_button.setFlat(True)
        self.maximize_button.setStyleSheet("background-color: white;")
        self.maximize_button.clicked.connect(self.maximizeWindow)

        close_button = QPushButton()
        close_icon = QIcon(GraphicsPath('Close.png'))
        close_button.setIcon(close_icon)
        close_button.setStyleSheet("background-color: white;")
        close_button.clicked.connect(self.closeWindow)

        line_frame = QFrame()
        line_frame.setFixedHeight(1)
        line_frame.setFrameShape(QFrame.HLine)
        line_frame.setFrameShadow(QFrame.Sunken)
        line_frame.setStyleSheet("border-color: black;")

        title_label = QLabel(f" {str(Assistantname).capitalize()} AI      ")
        title_label.setStyleSheet("color: black; font-size: 18px; background-color: white")

        home_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(0))
        message_button.clicked.connect(lambda: self.stacked_widget.setCurrentIndex(1))

        layout.addWidget(title_label)
        layout.addStretch(1)
        layout.addWidget(home_button)
        layout.addWidget(message_button)
        layout.addStretch(1)
        layout.addWidget(minimize_button)
        layout.addWidget(self.maximize_button)
        layout.addWidget(close_button)
        layout.addWidget(line_frame)

        self.draggable = True
        self.offset = None

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.fillRect(self.rect(), Qt.white)
        super().paintEvent(event)

    def minimizeWindow(self):
        self.parent().showMinimized()

    def maximizeWindow(self):
        if self.parent().isMaximized():
            self.parent().showNormal()
            self.maximize_button.setIcon(self.maximize_icon)
        else:
            self.parent().showMaximized()
            self.maximize_button.setIcon(self.restore_icon)

    def closeWindow(self):
        self.parent().close()

    def mousePressEvent(self, event):
        if self.draggable:
            self.offset = event.pos()

    def mouseMoveEvent(self, event):
        if self.draggable and self.offset:
            new_pos = event.globalPos() - self.offset
            self.parent().move(new_pos)

# --- MainWindow: Combines the top bar and the stacked screens ---
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.initUI()

    def initUI(self):
        desktop = QApplication.desktop()
        screen_width = desktop.screenGeometry().width()
        screen_height = desktop.screenGeometry().height()
        stacked_widget = QStackedWidget(self)

        initial_screen = InitialScreen()
        message_screen = MessageScreen()
        stacked_widget.addWidget(initial_screen)
        stacked_widget.addWidget(message_screen)

        self.setGeometry(0, 0, screen_width, screen_height)
        self.setStyleSheet("background-color: black;")
        top_bar = CustomTopBar(self, stacked_widget)
        self.setMenuWidget(top_bar)
        self.setCentralWidget(stacked_widget)

# Welcome message is now handled in main.py InitialExecution function

def GraphicalUserInterface():
    app = QApplication(sys.argv)
    window = MainWindow()

    # Start hidden during startup and loading screen
    window.hide()

    # Signal that GUI is ready (but hidden)
    signal_gui_ready()

    # Start a timer to check for loading screen completion
    loading_timer = QTimer()
    loading_timer.timeout.connect(lambda: check_and_show_window_after_loading(window, loading_timer))
    loading_timer.start(500)  # Check every 500ms

    print("🖥️ GUI initialized and hidden, waiting for 60-second loading screen to complete...")

    sys.exit(app.exec_())

def signal_gui_ready():
    """Signal that the GUI is ready but hidden"""
    try:
        with open(TempPath('GUI_Ready.data'), 'w', encoding='utf-8') as file:
            file.write("True")
        print("✅ GUI ready signal sent")
    except Exception as e:
        print(f"Error signaling GUI ready: {e}")

def is_loading_screen_complete():
    """Check if the loading screen has completed"""
    try:
        loading_file = TempPath('LoadingComplete.data')
        if os.path.exists(loading_file):
            with open(loading_file, 'r', encoding='utf-8') as file:
                return file.read().strip().lower() == "true"
    except Exception as e:
        print(f"Error checking loading status: {e}")
    return False

def is_startup_complete():
    """Check if the startup sequence is complete"""
    try:
        startup_file = TempPath('StartupComplete.data')
        if os.path.exists(startup_file):
            with open(startup_file, 'r', encoding='utf-8') as file:
                return file.read().strip().lower() == "true"
    except Exception as e:
        print(f"Error checking startup status: {e}")
    return False

def check_and_show_window_after_loading(window, timer):
    """Check if loading screen is complete and show the window"""
    if is_loading_screen_complete():
        print("🎉 Loading screen complete! Showing main interface...")
        window.show()
        window.raise_()  # Bring window to front
        window.activateWindow()  # Make it the active window
        timer.stop()

        # Clean up startup files
        try:
            loading_file = TempPath('LoadingComplete.data')
            if os.path.exists(loading_file):
                os.remove(loading_file)
            startup_file = TempPath('StartupComplete.data')
            if os.path.exists(startup_file):
                os.remove(startup_file)
            gui_ready_file = TempPath('GUI_Ready.data')
            if os.path.exists(gui_ready_file):
                os.remove(gui_ready_file)
            print("🧹 Startup coordination files cleaned up")
        except Exception as e:
            print(f"Error cleaning up startup files: {e}")

def check_and_show_window(window, timer):
    """Legacy function - Check if startup is complete and show the window"""
    if is_startup_complete():
        print("🎉 Startup complete! Showing main interface...")
        window.show()
        timer.stop()

        # Clean up startup files
        try:
            startup_file = TempPath('StartupComplete.data')
            if os.path.exists(startup_file):
                os.remove(startup_file)
            gui_ready_file = TempPath('GUI_Ready.data')
            if os.path.exists(gui_ready_file):
                os.remove(gui_ready_file)
        except Exception as e:
            print(f"Error cleaning up startup files: {e}")

if __name__ == "__main__":
    GraphicalUserInterface()
