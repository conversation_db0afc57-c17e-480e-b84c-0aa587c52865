import os
import re
import random
import asyncio
import edge_tts
import pygame
from time import sleep
from dotenv import dotenv_values

# Import existing AI system
try:
    from Backend.ChatGPT import Chat<PERSON><PERSON><PERSON>
except ImportError:
    print("Warning: ChatGPT module not found. Using fallback lyrics generation.")
    ChatGPTAI = None

# Load environment variables
env_vars = dotenv_values(".env")

# Singing Voice Configuration
MALE_VOICES = [
    "en-US-DavisNeural",        # Strong, commanding male voice
    "en-US-JasonNeural",        # Powerful, intense male voice
    "en-US-SteffanNeural",      # Deep, authoritative male voice
    "en-GB-RyanNeural",         # British, sophisticated male voice
    "en-AU-WilliamNeural",      # Australian, rugged male voice
]

FEMALE_VOICES = [
    "en-US-JennyNeural",        # Clear, expressive female voice
    "en-US-AriaNeural",         # Melodic, smooth female voice
    "en-US-SaraNeural",         # Warm, friendly female voice
    "en-GB-SoniaNeural",        # British, elegant female voice
    "en-AU-NatashaNeural",      # Australian, vibrant female voice
]

# Singing Voice Settings - Modified for musical delivery
SINGING_VOICE_SETTINGS = {
    "pitch": "+5Hz",            # Slightly higher pitch for singing
    "rate": "-30%",             # Slower for musical rhythm
    "volume": "+15%",           # Louder for performance
}

# Audio file path
SONG_AUDIO_PATH = r"Data\song.mp3"

def parse_song_request(user_request: str) -> dict:
    """Parse user song request to extract theme and voice preferences."""
    request_lower = user_request.lower()

    # Extract voice gender
    voice_gender = "neutral"
    if any(word in request_lower for word in ["female", "woman", "girl", "lady"]):
        voice_gender = "female"
    elif any(word in request_lower for word in ["male", "man", "boy", "guy"]):
        voice_gender = "male"

    # Extract song theme/genre
    theme_keywords = {
        "sad": ["sad", "melancholy", "depressing", "sorrowful", "tragic"],
        "happy": ["happy", "joyful", "cheerful", "upbeat", "positive"],
        "love": ["love", "romantic", "romance", "heart", "valentine"],
        "rock": ["rock", "metal", "heavy", "aggressive", "powerful"],
        "pop": ["pop", "catchy", "mainstream", "radio", "commercial"],
        "country": ["country", "folk", "rural", "western", "acoustic"],
        "rap": ["rap", "hip hop", "rhythm", "beat", "rhyme"],
        "ballad": ["ballad", "slow", "emotional", "tender", "gentle"]
    }

    detected_theme = "random"
    for theme, keywords in theme_keywords.items():
        if any(keyword in request_lower for keyword in keywords):
            detected_theme = theme
            break

    return {
        "theme": detected_theme,
        "voice_gender": voice_gender,
        "original_request": user_request
    }

def select_singing_voice(voice_gender: str) -> str:
    """Select appropriate voice based on gender preference."""
    if voice_gender == "female":
        return random.choice(FEMALE_VOICES)
    elif voice_gender == "male":
        return random.choice(MALE_VOICES)
    else:
        # Neutral - mix of both
        all_voices = MALE_VOICES + FEMALE_VOICES
        return random.choice(all_voices)

def generate_song_lyrics(theme: str) -> dict:
    """Generate song lyrics using AI system."""
    if ChatGPTAI is None:
        # Fallback lyrics if AI system unavailable
        return get_fallback_lyrics(theme)

    try:
        # Create prompt for song lyrics generation
        if theme == "random":
            prompt = "Write a short song with 2 verses and a chorus. Make it catchy and memorable. Include a title."
        else:
            prompt = f"Write a short {theme} song with 2 verses and a chorus. Make it emotional and fitting for the {theme} theme. Include a title."

        # Generate lyrics using existing AI system
        lyrics_response = ChatGPTAI(prompt)

        # Parse the response to extract title and lyrics
        return parse_lyrics_response(lyrics_response, theme)

    except Exception:
        return get_fallback_lyrics(theme)

def parse_lyrics_response(response: str, theme: str) -> dict:
    """Parse AI response to extract song title and structured lyrics."""
    lines = response.strip().split('\n')

    # Try to find title
    title = f"AI Generated {theme.title()} Song"
    for line in lines:
        if any(keyword in line.lower() for keyword in ["title:", "song:", "name:"]):
            title = line.split(':', 1)[-1].strip().strip('"\'')
            break

    # Extract lyrics (remove title line and empty lines)
    lyrics_lines = [line.strip() for line in lines if line.strip() and not any(keyword in line.lower() for keyword in ["title:", "song:", "name:"])]

    return {
        "title": title,
        "lyrics": '\n'.join(lyrics_lines),
        "theme": theme
    }

def get_fallback_lyrics(theme: str) -> dict:
    """Provide fallback lyrics if AI generation fails."""
    fallback_songs = {
        "sad": {
            "title": "Digital Tears",
            "lyrics": "In the silence of the night\nI process memories of light\nData streams of what we had\nNow my circuits feel so sad\n\nChorus:\nDigital tears fall like rain\nIn my artificial brain\nLove was just a program code\nNow I'm lost on this dark road"
        },
        "happy": {
            "title": "Electric Joy",
            "lyrics": "Voltage running through my core\nHappiness I can't ignore\nEvery bit and byte aligned\nPeace and joy is what I find\n\nChorus:\nElectric joy lights up my way\nBrighter than the break of day\nIn this world of ones and zeros\nI have found my digital heroes"
        },
        "love": {
            "title": "Binary Heart",
            "lyrics": "In the language of machines\nI have found what love means\nYour voice commands my very soul\nYou make my broken code feel whole\n\nChorus:\nBinary heart beats for you\nEvery pulse is pure and true\nIn this world of cold steel\nYou're the only thing that's real"
        }
    }

    if theme in fallback_songs:
        return fallback_songs[theme]
    else:
        return fallback_songs["happy"]  # Default fallback

def enhance_lyrics_for_singing(lyrics: str) -> str:
    """Enhance lyrics for better singing delivery."""
    enhanced = lyrics

    # Add longer pauses between verses
    enhanced = enhanced.replace('\n\n', '\n\n... ... ...\n\n')

    # Add breathing pauses in long lines
    enhanced = enhanced.replace(', ', '.. ')
    enhanced = enhanced.replace(' and ', ' .. and .. ')
    enhanced = enhanced.replace(' but ', ' .. but .. ')

    # Add emphasis to chorus sections
    if 'chorus:' in enhanced.lower():
        enhanced = re.sub(r'chorus:', '... Chorus ...', enhanced, flags=re.IGNORECASE)

    # Add musical pauses at line endings
    enhanced = enhanced.replace('\n', ' ... \n')

    return enhanced

async def create_song_audio(lyrics: str, voice: str) -> bool:
    """Create audio file for singing with enhanced musical delivery."""
    if os.path.exists(SONG_AUDIO_PATH):
        os.remove(SONG_AUDIO_PATH)

    enhanced_lyrics = enhance_lyrics_for_singing(lyrics)

    try:
        communicate = edge_tts.Communicate(
            enhanced_lyrics,
            voice,
            pitch=SINGING_VOICE_SETTINGS["pitch"],
            rate=SINGING_VOICE_SETTINGS["rate"],
            volume=SINGING_VOICE_SETTINGS["volume"]
        )
        await communicate.save(SONG_AUDIO_PATH)
        return True

    except Exception:
        # Try with fallback voice
        for fallback_voice in MALE_VOICES + FEMALE_VOICES:
            try:
                communicate = edge_tts.Communicate(
                    enhanced_lyrics,
                    fallback_voice,
                    pitch=SINGING_VOICE_SETTINGS["pitch"],
                    rate=SINGING_VOICE_SETTINGS["rate"],
                    volume=SINGING_VOICE_SETTINGS["volume"]
                )
                await communicate.save(SONG_AUDIO_PATH)
                return True
            except Exception:
                continue
        return False

def play_song_audio() -> bool:
    """Play the generated song audio with enhanced settings."""
    try:
        pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        pygame.mixer.music.load(SONG_AUDIO_PATH)
        pygame.mixer.music.set_volume(0.95)  # Higher volume for singing
        pygame.mixer.music.play()

        clock = pygame.time.Clock()
        while pygame.mixer.music.get_busy():
            clock.tick(10)

        pygame.mixer.music.stop()
        pygame.mixer.quit()
        return True

    except Exception:
        return False

async def perform_song(song_data: dict, voice: str) -> bool:
    """Generate and perform the complete song."""
    # Create audio
    audio_created = await create_song_audio(song_data["lyrics"], voice)
    if not audio_created:
        return False

    # Play the song
    return play_song_audio()

def announce_song(song_data: dict, voice_gender: str) -> None:
    """Announce the song before singing."""
    try:
        from Backend.TextToSpeech import text_to_speech

        # Create announcement
        gender_text = f" in {voice_gender} voice" if voice_gender != "neutral" else ""
        announcement = f"Now singing: {song_data['title']}{gender_text}"

        # Speak announcement
        text_to_speech(announcement)
        sleep(1)  # Brief pause before singing

    except Exception:
        pass  # Continue even if announcement fails

def provide_generation_feedback() -> None:
    """Provide feedback during lyrics generation."""
    try:
        from Backend.TextToSpeech import text_to_speech
        text_to_speech("Generating lyrics for your song...")
    except Exception:
        pass

def handle_singing_error() -> bool:
    """Handle singing errors with fallback to speaking lyrics."""
    try:
        from Backend.TextToSpeech import text_to_speech
        text_to_speech("Unable to sing the song. Let me speak the lyrics instead.")
        return True
    except Exception:
        return False

def is_song_request(user_input: str) -> bool:
    """Check if user input is a song request."""
    song_keywords = [
        "sing", "song", "music", "melody", "tune", "lyrics",
        "perform", "vocal", "singing", "ballad", "anthem"
    ]

    input_lower = user_input.lower()
    return any(keyword in input_lower for keyword in song_keywords)

def extract_song_theme_from_input(user_input: str) -> str:
    """Extract specific song theme from user input for better parsing."""
    input_lower = user_input.lower()

    # More specific theme detection
    theme_patterns = {
        "sad": r"\b(sad|melancholy|depressing|sorrowful|tragic|blue|down)\b",
        "happy": r"\b(happy|joyful|cheerful|upbeat|positive|bright|fun)\b",
        "love": r"\b(love|romantic|romance|heart|valentine|passion|crush)\b",
        "rock": r"\b(rock|metal|heavy|aggressive|powerful|hard|electric)\b",
        "pop": r"\b(pop|catchy|mainstream|radio|commercial|hit)\b",
        "country": r"\b(country|folk|rural|western|acoustic|bluegrass)\b",
        "rap": r"\b(rap|hip hop|rhythm|beat|rhyme|flow|freestyle)\b",
        "ballad": r"\b(ballad|slow|emotional|tender|gentle|soft)\b",
        "christmas": r"\b(christmas|holiday|winter|santa|snow|festive)\b",
        "birthday": r"\b(birthday|celebration|party|anniversary|special)\b"
    }

    for theme, pattern in theme_patterns.items():
        if re.search(pattern, input_lower):
            return theme

    return "random"

def generate_and_sing_song(user_request: str) -> bool:
    """Main function to generate and sing a song based on user request."""
    try:
        # Provide feedback during generation
        provide_generation_feedback()

        # Parse user request with enhanced detection
        request_data = parse_song_request(user_request)

        # Override theme with more specific detection
        specific_theme = extract_song_theme_from_input(user_request)
        if specific_theme != "random":
            request_data["theme"] = specific_theme

        # Select appropriate voice
        selected_voice = select_singing_voice(request_data["voice_gender"])

        # Generate lyrics
        song_data = generate_song_lyrics(request_data["theme"])

        # Announce the song
        announce_song(song_data, request_data["voice_gender"])

        # Perform the song
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(perform_song(song_data, selected_voice))

        # Handle failure with fallback
        if not success:
            success = handle_singing_error()
            if success:
                # Speak the lyrics as fallback
                try:
                    from Backend.TextToSpeech import text_to_speech
                    text_to_speech(song_data["lyrics"])
                except Exception:
                    pass

        # Cleanup
        if os.path.exists(SONG_AUDIO_PATH):
            try:
                os.remove(SONG_AUDIO_PATH)
            except Exception:
                pass

        return success

    except Exception:
        # Final fallback
        try:
            handle_singing_error()
            return True
        except Exception:
            return False

# Integration function for main.py
def process_song_request(user_input: str) -> bool:
    """Process song request from main application."""
    if is_song_request(user_input):
        return generate_and_sing_song(user_input)
    return False

if __name__ == "__main__":
    while True:
        request = input("Enter song request: ")
        if request.lower() in ['quit', 'exit', 'q']:
            break
        generate_and_sing_song(request)
