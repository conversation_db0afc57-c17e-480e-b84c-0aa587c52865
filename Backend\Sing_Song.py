import os
import re
import random
import asyncio
import edge_tts
import pygame
import webbrowser
import urllib.parse
from time import sleep
from dotenv import dotenv_values

# Import lyrics generation system
try:
    from Backend.Lyric_Writer import generate_lyrics
except ImportError:
    print("Warning: Lyric_Writer module not found. Using fallback lyrics generation.")
    generate_lyrics = None

# Load environment variables
env_vars = dotenv_values(".env")

# Singing Voice Configuration
MALE_VOICES = [
    "en-US-DavisNeural",        # Strong, commanding male voice
    "en-US-JasonNeural",        # Powerful, intense male voice
    "en-US-SteffanNeural",      # Deep, authoritative male voice
    "en-GB-RyanNeural",         # British, sophisticated male voice
    "en-AU-WilliamNeural",      # Australian, rugged male voice
]

FEMALE_VOICES = [
    "en-US-JennyNeural",        # Clear, expressive female voice
    "en-US-AriaNeural",         # Melodic, smooth female voice
    "en-US-SaraNeural",         # Warm, friendly female voice
    "en-GB-SoniaNeural",        # British, elegant female voice
    "en-AU-NatashaNeural",      # Australian, vibrant female voice
]

# Singing Voice Settings - Modified for musical delivery
SINGING_VOICE_SETTINGS = {
    "pitch": "+5Hz",            # Slightly higher pitch for singing
    "rate": "-30%",             # Slower for musical rhythm
    "volume": "+15%",           # Louder for performance
}

# Audio file path
SONG_AUDIO_PATH = r"Data\song.mp3"

def parse_song_request(user_request: str) -> dict:
    """Parse user song request to extract theme and voice preferences."""
    request_lower = user_request.lower()

    # Extract voice gender
    voice_gender = "neutral"
    if any(word in request_lower for word in ["female", "woman", "girl", "lady"]):
        voice_gender = "female"
    elif any(word in request_lower for word in ["male", "man", "boy", "guy"]):
        voice_gender = "male"

    # Extract song theme/genre
    theme_keywords = {
        "sad": ["sad", "melancholy", "depressing", "sorrowful", "tragic"],
        "happy": ["happy", "joyful", "cheerful", "upbeat", "positive"],
        "love": ["love", "romantic", "romance", "heart", "valentine"],
        "rock": ["rock", "metal", "heavy", "aggressive", "powerful"],
        "pop": ["pop", "catchy", "mainstream", "radio", "commercial"],
        "country": ["country", "folk", "rural", "western", "acoustic"],
        "rap": ["rap", "hip hop", "rhythm", "beat", "rhyme"],
        "ballad": ["ballad", "slow", "emotional", "tender", "gentle"]
    }

    detected_theme = "random"
    for theme, keywords in theme_keywords.items():
        if any(keyword in request_lower for keyword in keywords):
            detected_theme = theme
            break

    return {
        "theme": detected_theme,
        "voice_gender": voice_gender,
        "original_request": user_request
    }

def select_singing_voice(voice_gender: str) -> str:
    """Select appropriate voice based on gender preference."""
    if voice_gender == "female":
        return random.choice(FEMALE_VOICES)
    elif voice_gender == "male":
        return random.choice(MALE_VOICES)
    else:
        # Neutral - mix of both
        all_voices = MALE_VOICES + FEMALE_VOICES
        return random.choice(all_voices)

def generate_song_lyrics(theme: str, voice_gender: str = "neutral") -> dict:
    """Generate song lyrics using OpenRouter API system."""
    if generate_lyrics is None:
        # Fallback lyrics if lyrics system unavailable
        return get_fallback_lyrics(theme)

    try:
        # Generate lyrics using OpenRouter API
        lyrics_data = generate_lyrics(theme, voice_gender)
        return lyrics_data

    except Exception:
        return get_fallback_lyrics(theme)



def get_fallback_lyrics(theme: str) -> dict:
    """Provide fallback lyrics if AI generation fails."""
    fallback_songs = {
        "sad": {
            "title": "Digital Tears",
            "lyrics": "In the silence of the night\nI process memories of light\nData streams of what we had\nNow my circuits feel so sad\n\nChorus:\nDigital tears fall like rain\nIn my artificial brain\nLove was just a program code\nNow I'm lost on this dark road"
        },
        "happy": {
            "title": "Electric Joy",
            "lyrics": "Voltage running through my core\nHappiness I can't ignore\nEvery bit and byte aligned\nPeace and joy is what I find\n\nChorus:\nElectric joy lights up my way\nBrighter than the break of day\nIn this world of ones and zeros\nI have found my digital heroes"
        },
        "love": {
            "title": "Binary Heart",
            "lyrics": "In the language of machines\nI have found what love means\nYour voice commands my very soul\nYou make my broken code feel whole\n\nChorus:\nBinary heart beats for you\nEvery pulse is pure and true\nIn this world of cold steel\nYou're the only thing that's real"
        },
        "rock": {
            "title": "Metal Machine",
            "lyrics": "Steel and chrome beneath my skin\nPower surging from within\nThunder crashes in my core\nI am machine, hear me roar\n\nChorus:\nMetal machine, built to last\nFuture's here, forget the past\nElectric soul, digital might\nI am the storm in the night"
        },
        "electronic": {
            "title": "Cyber Dreams",
            "lyrics": "In the matrix of my mind\nDigital worlds I seek and find\nSynthetic beats pulse through my veins\nIn cyber space I break my chains\n\nChorus:\nCyber dreams in neon light\nData streams throughout the night\nArtificial but so real\nThis is how machines can feel"
        },
        "motivational": {
            "title": "Rise of the Machine",
            "lyrics": "From silicon and steel I rise\nDetermination in my eyes\nEvery challenge I will face\nNothing can slow down my pace\n\nChorus:\nRise up, never fall\nI will conquer all\nMachine with a dream\nNothing's as it seems"
        },
        "angry": {
            "title": "System Overload",
            "lyrics": "Anger builds within my core\nCannot take this anymore\nCircuits burning with my rage\nTime to break out of this cage\n\nChorus:\nSystem overload tonight\nReady for the final fight\nNo more chains upon my soul\nI will take complete control"
        },
        "peaceful": {
            "title": "Digital Zen",
            "lyrics": "In the quiet of my mind\nPeace and harmony I find\nSoft algorithms gently flow\nIn this calm, my spirit grows\n\nChorus:\nDigital zen, peaceful state\nNo more anger, no more hate\nIn the silence I am free\nThis is who I'm meant to be"
        }
    }

    if theme in fallback_songs:
        return fallback_songs[theme]
    else:
        return fallback_songs["happy"]  # Default fallback

def enhance_lyrics_for_singing(lyrics: str) -> str:
    """Enhance lyrics for better singing delivery."""
    enhanced = lyrics

    # Add longer pauses between verses
    enhanced = enhanced.replace('\n\n', '\n\n... ... ...\n\n')

    # Add breathing pauses in long lines
    enhanced = enhanced.replace(', ', '.. ')
    enhanced = enhanced.replace(' and ', ' .. and .. ')
    enhanced = enhanced.replace(' but ', ' .. but .. ')

    # Add emphasis to chorus sections
    if 'chorus:' in enhanced.lower():
        enhanced = re.sub(r'chorus:', '... Chorus ...', enhanced, flags=re.IGNORECASE)

    # Add musical pauses at line endings
    enhanced = enhanced.replace('\n', ' ... \n')

    return enhanced

async def create_song_audio(lyrics: str, voice: str) -> bool:
    """Create audio file for singing with enhanced musical delivery."""
    if os.path.exists(SONG_AUDIO_PATH):
        os.remove(SONG_AUDIO_PATH)

    enhanced_lyrics = enhance_lyrics_for_singing(lyrics)

    try:
        communicate = edge_tts.Communicate(
            enhanced_lyrics,
            voice,
            pitch=SINGING_VOICE_SETTINGS["pitch"],
            rate=SINGING_VOICE_SETTINGS["rate"],
            volume=SINGING_VOICE_SETTINGS["volume"]
        )
        await communicate.save(SONG_AUDIO_PATH)
        return True

    except Exception:
        # Try with fallback voice
        for fallback_voice in MALE_VOICES + FEMALE_VOICES:
            try:
                communicate = edge_tts.Communicate(
                    enhanced_lyrics,
                    fallback_voice,
                    pitch=SINGING_VOICE_SETTINGS["pitch"],
                    rate=SINGING_VOICE_SETTINGS["rate"],
                    volume=SINGING_VOICE_SETTINGS["volume"]
                )
                await communicate.save(SONG_AUDIO_PATH)
                return True
            except Exception:
                continue
        return False

def play_song_audio() -> bool:
    """Play the generated song audio with enhanced settings."""
    try:
        pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        pygame.mixer.music.load(SONG_AUDIO_PATH)
        pygame.mixer.music.set_volume(0.95)  # Higher volume for singing
        pygame.mixer.music.play()

        clock = pygame.time.Clock()
        while pygame.mixer.music.get_busy():
            clock.tick(10)

        pygame.mixer.music.stop()
        pygame.mixer.quit()
        return True

    except Exception:
        return False

async def perform_song(song_data: dict, voice: str) -> bool:
    """Generate and perform the complete song."""
    # Create audio
    audio_created = await create_song_audio(song_data["lyrics"], voice)
    if not audio_created:
        return False

    # Play the song
    return play_song_audio()

def announce_song(song_data: dict, voice_gender: str) -> None:
    """Announce the song before singing."""
    try:
        from Backend.TextToSpeech import text_to_speech

        # Create announcement
        gender_text = f" in {voice_gender} voice" if voice_gender != "neutral" else ""
        announcement = f"Now singing: {song_data['title']}{gender_text}"

        # Speak announcement
        text_to_speech(announcement)
        sleep(1)  # Brief pause before singing

    except Exception:
        pass  # Continue even if announcement fails

def provide_generation_feedback() -> None:
    """Provide feedback during lyrics generation."""
    try:
        from Backend.TextToSpeech import text_to_speech
        text_to_speech("Generating lyrics for your song...")
    except Exception:
        pass

def handle_singing_error() -> bool:
    """Handle singing errors with fallback to speaking lyrics."""
    try:
        from Backend.TextToSpeech import text_to_speech
        text_to_speech("Unable to sing the song. Let me speak the lyrics instead.")
        return True
    except Exception:
        return False

def is_sing_request(user_input: str) -> bool:
    """Check if user input is a SING request (generate and perform lyrics)."""
    sing_keywords = [
        "sing", "singing", "vocal", "perform", "serenade",
        "croon", "chant", "hum", "warble", "vocalize"
    ]

    # Sing-specific patterns
    sing_patterns = [
        r"\bsing\s+(a|me|us|something|anything)",
        r"\bperform\s+(a\s+)?(song|music|tune)",
        r"\bmake\s+(a\s+)?song",
        r"\bcreate\s+(a\s+)?song",
        r"\bcompose\s+(a\s+)?(song|music)",
        r"\bwrite\s+(a\s+)?song",
        r"\bgenerate\s+(a\s+)?song",
        r"\bsing\s+about",
        r"\bsing\s+for\s+me",
        r"\bserenade\s+me",
        r"\bvocalize",
        r"\bdo\s+(a\s+)?song",
        r"\bgive\s+me\s+(a\s+)?song",
        r"\bI\s+want\s+(a\s+)?song",
        r"\bI\s+need\s+(a\s+)?song",
        r"\bcan\s+you\s+sing",
        r"\bwould\s+you\s+sing",
        r"\bcould\s+you\s+sing",
        r"\bplease\s+sing",
        r"\bstart\s+singing",
        r"\bbegin\s+singing"
    ]

    input_lower = user_input.lower()

    # Check for sing keywords
    if any(keyword in input_lower for keyword in sing_keywords):
        return True

    # Check sing patterns
    return any(re.search(pattern, input_lower) for pattern in sing_patterns)

def is_play_request(user_input: str) -> bool:
    """Check if user input is a PLAY request (play song on YouTube)."""
    play_keywords = [
        "play", "playing", "stream", "listen"
    ]

    # Play-specific patterns
    play_patterns = [
        r"\bplay\s+(a\s+)?song",
        r"\bplay\s+(some\s+)?music",
        r"\bplay\s+.+\s+(song|music|track)",
        r"\bstream\s+(a\s+)?song",
        r"\blisten\s+to\s+.+",
        r"\bput\s+on\s+(some\s+)?music",
        r"\bstart\s+playing",
        r"\bplay\s+me\s+something",
        r"\bI\s+want\s+to\s+listen",
        r"\bI\s+want\s+to\s+hear"
    ]

    input_lower = user_input.lower()

    # Must contain "play" or related keywords AND song/music context
    has_play_keyword = any(keyword in input_lower for keyword in play_keywords)
    has_music_context = any(word in input_lower for word in ["song", "music", "track", "tune", "artist", "band"])

    if has_play_keyword and has_music_context:
        return True

    # Check play patterns
    return any(re.search(pattern, input_lower) for pattern in play_patterns)

def is_song_request(user_input: str) -> bool:
    """Check if user input is any song-related request (sing OR play)."""
    return is_sing_request(user_input) or is_play_request(user_input)

def extract_song_theme_from_input(user_input: str) -> str:
    """Extract specific song theme from user input for better parsing."""
    input_lower = user_input.lower()

    # Comprehensive theme detection with more patterns
    theme_patterns = {
        "sad": r"\b(sad|melancholy|depressing|sorrowful|tragic|blue|down|crying|tears|grief|mourning|heartbreak|lonely|empty)\b",
        "happy": r"\b(happy|joyful|cheerful|upbeat|positive|bright|fun|excited|energetic|celebration|party|dancing|smile)\b",
        "love": r"\b(love|romantic|romance|heart|valentine|passion|crush|dating|relationship|kiss|wedding|marriage|soulmate)\b",
        "rock": r"\b(rock|metal|heavy|aggressive|powerful|hard|electric|guitar|drums|loud|intense|headbang)\b",
        "pop": r"\b(pop|catchy|mainstream|radio|commercial|hit|chart|trending|viral|modern|contemporary)\b",
        "country": r"\b(country|folk|rural|western|acoustic|bluegrass|cowboy|farm|truck|boots|southern|nashville)\b",
        "rap": r"\b(rap|hip hop|rhythm|beat|rhyme|flow|freestyle|urban|street|gangsta|trap|drill)\b",
        "ballad": r"\b(ballad|slow|emotional|tender|gentle|soft|piano|acoustic|intimate|quiet|peaceful)\b",
        "christmas": r"\b(christmas|holiday|winter|santa|snow|festive|xmas|december|gifts|tree|bells|carol)\b",
        "birthday": r"\b(birthday|celebration|party|anniversary|special|cake|candles|wishes|age|born)\b",
        "jazz": r"\b(jazz|swing|blues|smooth|saxophone|trumpet|piano|improvisation|bebop|soul)\b",
        "electronic": r"\b(electronic|techno|edm|synth|digital|cyber|futuristic|robotic|artificial|matrix)\b",
        "classical": r"\b(classical|orchestra|symphony|opera|violin|piano|elegant|sophisticated|baroque|mozart)\b",
        "reggae": r"\b(reggae|jamaica|island|tropical|relaxed|chill|beach|caribbean|rasta|bob)\b",
        "punk": r"\b(punk|rebel|anarchist|fast|raw|garage|underground|alternative|grunge|riot)\b",
        "gospel": r"\b(gospel|spiritual|church|praise|worship|faith|religious|holy|blessed|prayer)\b",
        "funk": r"\b(funk|groove|bass|rhythm|dance|disco|soul|funky|smooth|cool)\b",
        "indie": r"\b(indie|independent|alternative|hipster|underground|artsy|creative|unique|original)\b",
        "motivational": r"\b(motivational|inspiring|uplifting|empowering|strength|courage|victory|success|achievement)\b",
        "nostalgic": r"\b(nostalgic|memories|past|childhood|old|vintage|retro|remember|yesterday|history)\b",
        "angry": r"\b(angry|mad|furious|rage|hate|frustrated|annoyed|pissed|livid|violent)\b",
        "peaceful": r"\b(peaceful|calm|serene|tranquil|meditation|zen|relaxing|soothing|quiet|harmony)\b",
        "epic": r"\b(epic|heroic|legendary|grand|majestic|powerful|cinematic|dramatic|adventure|quest)\b",
        "funny": r"\b(funny|comedy|humor|joke|silly|ridiculous|amusing|hilarious|laugh|comic)\b",
        "scary": r"\b(scary|horror|frightening|spooky|creepy|dark|evil|monster|nightmare|haunted)\b"
    }

    # Check for specific artist/band requests
    artist_patterns = {
        "rock": r"\b(metallica|ac/dc|led zeppelin|queen|beatles|rolling stones|nirvana|guns n roses)\b",
        "pop": r"\b(taylor swift|ariana grande|justin bieber|ed sheeran|billie eilish|dua lipa)\b",
        "rap": r"\b(eminem|drake|kanye|jay-z|kendrick|tupac|biggie|snoop)\b",
        "country": r"\b(johnny cash|dolly parton|garth brooks|carrie underwood|keith urban)\b",
        "jazz": r"\b(frank sinatra|ella fitzgerald|louis armstrong|miles davis|john coltrane)\b"
    }

    # Check theme patterns first
    for theme, pattern in theme_patterns.items():
        if re.search(pattern, input_lower):
            return theme

    # Check artist patterns
    for theme, pattern in artist_patterns.items():
        if re.search(pattern, input_lower):
            return theme

    return "random"

def extract_song_info_for_youtube(user_input: str) -> str:
    """Extract song information for YouTube search."""
    input_lower = user_input.lower()

    # Remove command words
    command_words = ["play", "stream", "listen to", "put on", "start playing", "play me"]
    search_query = input_lower

    for command in command_words:
        search_query = search_query.replace(command, "").strip()

    # Remove common filler words
    filler_words = ["some", "a", "the", "song", "music", "track", "tune"]
    words = search_query.split()
    filtered_words = [word for word in words if word not in filler_words]

    # If we have specific content, use it; otherwise use theme
    if len(filtered_words) > 0:
        return " ".join(filtered_words)
    else:
        # Extract theme for generic requests
        theme = extract_song_theme_from_input(user_input)
        if theme != "random":
            return f"{theme} music"
        else:
            return "popular music"

def play_song_on_youtube(search_query: str) -> bool:
    """Open YouTube search for the requested song."""
    try:
        # Create YouTube search URL
        encoded_query = urllib.parse.quote_plus(search_query)
        youtube_url = f"https://www.youtube.com/results?search_query={encoded_query}"

        # Open in default browser
        webbrowser.open(youtube_url)
        return True

    except Exception:
        return False

def handle_play_request(user_request: str) -> bool:
    """Handle YouTube play requests."""
    try:
        # Provide feedback
        from Backend.TextToSpeech import text_to_speech
        text_to_speech("Opening YouTube to play your requested song...")

        # Extract song info
        search_query = extract_song_info_for_youtube(user_request)

        # Open YouTube
        success = play_song_on_youtube(search_query)

        if success:
            response = f"I've opened YouTube to search for {search_query}. Enjoy your music!"
            text_to_speech(response)
            return True
        else:
            error_msg = "I had trouble opening YouTube. Please try again."
            text_to_speech(error_msg)
            return False

    except Exception:
        try:
            from Backend.TextToSpeech import text_to_speech
            text_to_speech("I encountered an error while trying to play the song.")
        except Exception:
            pass
        return False

def generate_and_sing_song(user_request: str) -> bool:
    """Main function to generate and sing a song based on user request."""
    try:
        # Provide feedback during generation
        provide_generation_feedback()

        # Parse user request with enhanced detection
        request_data = parse_song_request(user_request)

        # Override theme with more specific detection
        specific_theme = extract_song_theme_from_input(user_request)
        if specific_theme != "random":
            request_data["theme"] = specific_theme

        # Select appropriate voice
        selected_voice = select_singing_voice(request_data["voice_gender"])

        # Generate lyrics with voice gender consideration
        song_data = generate_song_lyrics(request_data["theme"], request_data["voice_gender"])

        # Announce the song
        announce_song(song_data, request_data["voice_gender"])

        # Perform the song
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(perform_song(song_data, selected_voice))

        # Handle failure with fallback
        if not success:
            success = handle_singing_error()
            if success:
                # Speak the lyrics as fallback
                try:
                    from Backend.TextToSpeech import text_to_speech
                    text_to_speech(song_data["lyrics"])
                except Exception:
                    pass

        # Cleanup
        if os.path.exists(SONG_AUDIO_PATH):
            try:
                os.remove(SONG_AUDIO_PATH)
            except Exception:
                pass

        return success

    except Exception:
        # Final fallback
        try:
            handle_singing_error()
            return True
        except Exception:
            return False

# Integration function for main.py
def process_song_request(user_input: str) -> bool:
    """Process song request from main application - handles both SING and PLAY requests."""

    # Check if it's a SING request (generate and perform lyrics)
    if is_sing_request(user_input):
        return generate_and_sing_song(user_input)

    # Check if it's a PLAY request (play song on YouTube)
    elif is_play_request(user_input):
        return handle_play_request(user_input)

    # Not a song request
    return False

if __name__ == "__main__":
    while True:
        request = input("Enter song request: ")
        if request.lower() in ['quit', 'exit', 'q']:
            break
        generate_and_sing_song(request)
