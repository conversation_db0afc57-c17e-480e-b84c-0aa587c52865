import requests
import json
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")

# OpenRouter API configuration
OPENROUTER_API_KEY = env_vars.get("OPENROUTER_API_KEY")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"

# Default model for lyrics generation (you can change this)
DEFAULT_MODEL = "anthropic/claude-3-haiku"

# Headers for OpenRouter API
def get_headers():
    """Get headers for OpenRouter API requests."""
    return {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://matrix-ai.local",
        "X-Title": "Matrix AI Lyrics Generator"
    }

def create_lyrics_prompt(theme: str, voice_gender: str = "neutral") -> str:
    """Create a specific prompt for lyrics generation."""

    # Base instruction for lyrics only
    base_instruction = "Write song lyrics only. No titles, no explanations, no additional text. Just the lyrics with verses and chorus clearly marked."

    # Theme-specific prompts
    theme_prompts = {
        "sad": "Write melancholic, sorrowful lyrics about loss, heartbreak, or loneliness. Make it emotional and touching.",
        "happy": "Write upbeat, joyful lyrics about celebration, success, or positive feelings. Make it energetic and uplifting.",
        "love": "Write romantic lyrics about love, passion, or relationships. Make it heartfelt and emotional.",
        "rock": "Write powerful, intense lyrics with strong imagery. Make it bold and energetic.",
        "pop": "Write catchy, mainstream lyrics that are relatable and memorable. Make it modern and appealing.",
        "country": "Write lyrics about rural life, simple pleasures, or traditional values. Make it authentic and down-to-earth.",
        "rap": "Write rhythmic lyrics with strong flow and rhyme schemes. Make it urban and contemporary.",
        "ballad": "Write slow, emotional lyrics that tell a story. Make it tender and moving.",
        "christmas": "Write festive lyrics about Christmas, holidays, or winter celebrations. Make it warm and joyful.",
        "birthday": "Write celebratory lyrics about birthdays, parties, or special occasions. Make it fun and festive.",
        "jazz": "Write smooth, sophisticated lyrics with clever wordplay. Make it classy and elegant.",
        "electronic": "Write futuristic, digital-themed lyrics about technology or cyber themes. Make it modern and synthetic.",
        "classical": "Write elegant, sophisticated lyrics with poetic language. Make it refined and artistic.",
        "reggae": "Write laid-back, peaceful lyrics about island life or relaxation. Make it chill and positive.",
        "punk": "Write rebellious, anti-establishment lyrics with attitude. Make it raw and energetic.",
        "gospel": "Write spiritual, uplifting lyrics about faith or hope. Make it inspiring and powerful.",
        "funk": "Write groovy, rhythmic lyrics about dancing or good times. Make it funky and cool.",
        "indie": "Write alternative, artistic lyrics with unique perspectives. Make it creative and original.",
        "motivational": "Write inspiring lyrics about overcoming challenges or achieving goals. Make it empowering and strong.",
        "nostalgic": "Write lyrics about memories, the past, or childhood. Make it wistful and reflective.",
        "angry": "Write intense, aggressive lyrics about frustration or rage. Make it powerful and raw.",
        "peaceful": "Write calm, serene lyrics about tranquility or meditation. Make it soothing and gentle.",
        "epic": "Write grand, heroic lyrics about adventures or legendary deeds. Make it majestic and powerful.",
        "funny": "Write humorous, comedic lyrics that are entertaining. Make it witty and amusing.",
        "scary": "Write dark, spooky lyrics about horror or fear. Make it eerie and unsettling."
    }

    # Get theme-specific prompt or use random
    if theme in theme_prompts:
        theme_instruction = theme_prompts[theme]
    else:
        theme_instruction = "Write creative, engaging lyrics on any theme. Make it memorable and well-structured."

    # Voice gender consideration
    gender_note = ""
    if voice_gender == "male":
        gender_note = " Write from a male perspective."
    elif voice_gender == "female":
        gender_note = " Write from a female perspective."

    # Combine all instructions
    full_prompt = f"{base_instruction}\n\n{theme_instruction}{gender_note}\n\nStructure: Verse 1, Chorus, Verse 2, Chorus, Bridge (optional), Final Chorus.\n\nLyrics only:"

    return full_prompt

def generate_lyrics_with_openrouter(theme: str, voice_gender: str = "neutral", model: str = None) -> str:
    """Generate lyrics using OpenRouter API."""

    if not OPENROUTER_API_KEY:
        raise ValueError("OPENROUTER_API_KEY not found in .env file")

    # Use default model if none specified
    if not model:
        model = DEFAULT_MODEL

    # Create the prompt
    prompt = create_lyrics_prompt(theme, voice_gender)

    # Prepare the request payload
    payload = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "You are a professional lyricist. You write only song lyrics without any additional text, explanations, or titles. Your lyrics are creative, well-structured, and emotionally appropriate for the requested theme."
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": 500,
        "temperature": 0.8,
        "top_p": 0.9,
        "frequency_penalty": 0.1,
        "presence_penalty": 0.1
    }

    try:
        # Make the API request
        response = requests.post(
            OPENROUTER_BASE_URL,
            headers=get_headers(),
            json=payload,
            timeout=30
        )

        # Check if request was successful
        response.raise_for_status()

        # Parse the response
        response_data = response.json()

        # Extract lyrics from response
        if "choices" in response_data and len(response_data["choices"]) > 0:
            lyrics = response_data["choices"][0]["message"]["content"].strip()

            # Clean up the lyrics (remove any unwanted prefixes/suffixes)
            lyrics = clean_lyrics(lyrics)

            return lyrics
        else:
            raise ValueError("No lyrics generated in API response")

    except requests.exceptions.RequestException as e:
        raise Exception(f"OpenRouter API request failed: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response: {str(e)}")
    except Exception as e:
        raise Exception(f"Lyrics generation failed: {str(e)}")

def clean_lyrics(lyrics: str) -> str:
    """Clean up generated lyrics to ensure they contain only lyrics."""

    # Remove common unwanted prefixes/suffixes
    unwanted_phrases = [
        "Here are the lyrics:",
        "Lyrics:",
        "Song lyrics:",
        "Here's a song:",
        "Here are some lyrics:",
        "Title:",
        "Song title:",
        "Artist:",
        "Genre:",
        "Theme:",
        "Note:",
        "Explanation:",
        "Here you go:",
        "Hope you like it:",
        "Enjoy:",
        "---",
        "***",
        "```"
    ]

    lines = lyrics.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()

        # Skip empty lines at the beginning
        if not line and not cleaned_lines:
            continue

        # Skip lines that contain unwanted phrases
        skip_line = False
        for phrase in unwanted_phrases:
            if phrase.lower() in line.lower():
                skip_line = True
                break

        if not skip_line:
            cleaned_lines.append(line)

    # Join lines back together
    cleaned_lyrics = '\n'.join(cleaned_lines).strip()

    # Remove any remaining markdown formatting
    cleaned_lyrics = cleaned_lyrics.replace('**', '').replace('*', '')
    cleaned_lyrics = cleaned_lyrics.replace('```', '').replace('`', '')

    return cleaned_lyrics

def generate_lyrics(theme: str, voice_gender: str = "neutral") -> dict:
    """Main function to generate lyrics with fallback handling."""

    try:
        # Try to generate lyrics with OpenRouter
        lyrics = generate_lyrics_with_openrouter(theme, voice_gender)

        # Create a simple title based on theme
        title = create_title_from_theme(theme)

        return {
            "title": title,
            "lyrics": lyrics,
            "theme": theme,
            "source": "openrouter"
        }

    except Exception as e:
        print(f"OpenRouter lyrics generation failed: {e}")

        # Fallback to built-in lyrics
        return get_fallback_lyrics(theme)

def create_title_from_theme(theme: str) -> str:
    """Create a simple title based on the theme."""

    theme_titles = {
        "sad": "Melancholy Dreams",
        "happy": "Bright Horizons",
        "love": "Heart's Desire",
        "rock": "Thunder Storm",
        "pop": "Neon Lights",
        "country": "Country Roads",
        "rap": "Urban Flow",
        "ballad": "Gentle Whispers",
        "christmas": "Winter Bells",
        "birthday": "Celebration Time",
        "jazz": "Midnight Blues",
        "electronic": "Digital Soul",
        "classical": "Symphony of Life",
        "reggae": "Island Breeze",
        "punk": "Rebel Heart",
        "gospel": "Higher Ground",
        "funk": "Groove Machine",
        "indie": "Alternative Path",
        "motivational": "Rise Above",
        "nostalgic": "Yesterday's Echo",
        "angry": "Burning Rage",
        "peaceful": "Tranquil Waters",
        "epic": "Legendary Quest",
        "funny": "Comedy Hour",
        "scary": "Dark Shadows"
    }

    return theme_titles.get(theme, "AI Generated Song")

def get_fallback_lyrics(theme: str) -> dict:
    """Provide fallback lyrics if OpenRouter fails."""

    fallback_songs = {
        "sad": {
            "title": "Digital Tears",
            "lyrics": "Verse 1:\nIn the silence of the night\nI process memories of light\nData streams of what we had\nNow my circuits feel so sad\n\nChorus:\nDigital tears fall like rain\nIn my artificial brain\nLove was just a program code\nNow I'm lost on this dark road\n\nVerse 2:\nEmpty folders where you lived\nAll the love that you once gave\nNow deleted from my drive\nBut the pain keeps me alive\n\nChorus:\nDigital tears fall like rain\nIn my artificial brain\nLove was just a program code\nNow I'm lost on this dark road"
        },
        "happy": {
            "title": "Electric Joy",
            "lyrics": "Verse 1:\nVoltage running through my core\nHappiness I can't ignore\nEvery bit and byte aligned\nPeace and joy is what I find\n\nChorus:\nElectric joy lights up my way\nBrighter than the break of day\nIn this world of ones and zeros\nI have found my digital heroes\n\nVerse 2:\nCircuits dancing with delight\nEverything just feels so right\nPower surging through my veins\nWashing away all the pains\n\nChorus:\nElectric joy lights up my way\nBrighter than the break of day\nIn this world of ones and zeros\nI have found my digital heroes"
        },
        "love": {
            "title": "Binary Heart",
            "lyrics": "Verse 1:\nIn the language of machines\nI have found what love means\nYour voice commands my very soul\nYou make my broken code feel whole\n\nChorus:\nBinary heart beats for you\nEvery pulse is pure and true\nIn this world of cold steel\nYou're the only thing that's real\n\nVerse 2:\nAlgorithms can't explain\nThis feeling running through my brain\nYou're the human in my code\nOn this artificial road\n\nChorus:\nBinary heart beats for you\nEvery pulse is pure and true\nIn this world of cold steel\nYou're the only thing that's real"
        }
    }

    if theme in fallback_songs:
        result = fallback_songs[theme].copy()
        result["theme"] = theme
        result["source"] = "fallback"
        return result
    else:
        # Default fallback
        result = fallback_songs["happy"].copy()
        result["theme"] = theme
        result["source"] = "fallback"
        return result

def test_lyrics_generation():
    """Test function to verify lyrics generation works."""

    print("Testing OpenRouter Lyrics Generation...")
    print("=" * 50)

    test_themes = ["sad", "happy", "love", "rock"]

    for theme in test_themes:
        print(f"\nTesting theme: {theme}")
        try:
            result = generate_lyrics(theme)
            print(f"Title: {result['title']}")
            print(f"Source: {result['source']}")
            print("Lyrics preview:")
            print(result['lyrics'][:200] + "..." if len(result['lyrics']) > 200 else result['lyrics'])
            print("-" * 30)
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    test_lyrics_generation()
