# Matrix AI - Lyric Writer System Documentation

## Overview
The `Backend/Lyric_Writer.py` module provides a dedicated lyrics generation system using the OpenRouter API. It generates clean, structured song lyrics without any additional text, specifically designed for the Matrix AI singing system.

## Features

### 🎵 **OpenRouter API Integration**
- Uses OpenRouter API for high-quality lyrics generation
- Supports multiple AI models (default: Claude-3-Haiku)
- Configurable through environment variables
- Professional-grade lyrics generation

### 🎭 **Theme-Specific Generation**
- **25+ Supported Themes**: sad, happy, love, rock, pop, country, rap, ballad, christmas, birthday, jazz, electronic, classical, reggae, punk, gospel, funk, indie, motivational, nostalgic, angry, peaceful, epic, funny, scary
- **Custom Prompts**: Each theme has specialized prompts for appropriate lyrics
- **Voice Gender Consideration**: Lyrics adapted for male/female perspectives
- **Structured Output**: Verses, chorus, and bridge sections

### 🛡️ **Robust Error Handling**
- **Fallback System**: Built-in lyrics if API fails
- **Clean Output**: Removes unwanted text and formatting
- **Timeout Protection**: 30-second request timeout
- **Graceful Degradation**: Always provides lyrics

## Configuration

### 🔧 **Environment Variables (.env file)**
```env
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 🎛️ **API Settings**
```python
# Default model (can be changed)
DEFAULT_MODEL = "anthropic/claude-3-haiku"

# API endpoint
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"

# Request parameters
max_tokens = 500
temperature = 0.8
top_p = 0.9
frequency_penalty = 0.1
presence_penalty = 0.1
```

## Usage

### 🎤 **Basic Usage**
```python
from Backend.Lyric_Writer import generate_lyrics

# Generate lyrics for a theme
result = generate_lyrics("sad", "female")

print(f"Title: {result['title']}")
print(f"Theme: {result['theme']}")
print(f"Source: {result['source']}")
print(f"Lyrics:\n{result['lyrics']}")
```

### 🎯 **Function Parameters**
```python
def generate_lyrics(theme: str, voice_gender: str = "neutral") -> dict:
    """
    Generate song lyrics using OpenRouter API.
    
    Args:
        theme: Song theme (sad, happy, love, rock, etc.)
        voice_gender: "male", "female", or "neutral"
    
    Returns:
        dict: {
            "title": "Song Title",
            "lyrics": "Verse 1:\n...\nChorus:\n...",
            "theme": "requested_theme",
            "source": "openrouter" or "fallback"
        }
    """
```

## Theme-Specific Prompts

### 🎭 **Emotional Themes**
```python
"sad": "Write melancholic, sorrowful lyrics about loss, heartbreak, or loneliness."
"happy": "Write upbeat, joyful lyrics about celebration, success, or positive feelings."
"love": "Write romantic lyrics about love, passion, or relationships."
"angry": "Write intense, aggressive lyrics about frustration or rage."
"peaceful": "Write calm, serene lyrics about tranquility or meditation."
```

### 🎸 **Musical Genres**
```python
"rock": "Write powerful, intense lyrics with strong imagery."
"pop": "Write catchy, mainstream lyrics that are relatable and memorable."
"country": "Write lyrics about rural life, simple pleasures, or traditional values."
"rap": "Write rhythmic lyrics with strong flow and rhyme schemes."
"jazz": "Write smooth, sophisticated lyrics with clever wordplay."
```

### 🎉 **Special Occasions**
```python
"christmas": "Write festive lyrics about Christmas, holidays, or winter celebrations."
"birthday": "Write celebratory lyrics about birthdays, parties, or special occasions."
"motivational": "Write inspiring lyrics about overcoming challenges or achieving goals."
"nostalgic": "Write lyrics about memories, the past, or childhood."
```

## API Response Processing

### 🧹 **Lyrics Cleaning**
The system automatically removes unwanted text:
```python
# Removed phrases
"Here are the lyrics:", "Lyrics:", "Song lyrics:", "Title:", 
"Note:", "Explanation:", "---", "***", "```"

# Removed formatting
"**bold**" → "bold"
"*italic*" → "italic"
"```code```" → "code"
```

### 🏷️ **Title Generation**
Automatic title creation based on theme:
```python
theme_titles = {
    "sad": "Melancholy Dreams",
    "happy": "Bright Horizons", 
    "love": "Heart's Desire",
    "rock": "Thunder Storm",
    "pop": "Neon Lights",
    # ... more themes
}
```

## Integration with Singing System

### 🔗 **Sing_Song.py Integration**
```python
# Updated import in Sing_Song.py
from Backend.Lyric_Writer import generate_lyrics

# Updated function call
def generate_song_lyrics(theme: str, voice_gender: str = "neutral") -> dict:
    if generate_lyrics is None:
        return get_fallback_lyrics(theme)
    
    try:
        lyrics_data = generate_lyrics(theme, voice_gender)
        return lyrics_data
    except Exception:
        return get_fallback_lyrics(theme)
```

### 🎵 **Voice Gender Consideration**
```python
# Male perspective lyrics
voice_gender = "male"
# Adds: "Write from a male perspective."

# Female perspective lyrics  
voice_gender = "female"
# Adds: "Write from a female perspective."

# Neutral lyrics
voice_gender = "neutral"
# No gender-specific instruction
```

## Error Handling

### 🛡️ **Fallback System**
```python
# If OpenRouter API fails, uses built-in lyrics
fallback_songs = {
    "sad": {
        "title": "Digital Tears",
        "lyrics": "Verse 1:\nIn the silence of the night..."
    },
    "happy": {
        "title": "Electric Joy", 
        "lyrics": "Verse 1:\nVoltage running through my core..."
    },
    # ... more fallback songs
}
```

### ⚠️ **Error Types Handled**
- **API Key Missing**: Clear error message
- **Network Timeout**: 30-second timeout protection
- **Invalid Response**: JSON parsing error handling
- **Rate Limiting**: Graceful fallback to built-in lyrics
- **Model Unavailable**: Automatic fallback system

## Testing

### 🧪 **Test Function**
```python
def test_lyrics_generation():
    """Test function to verify lyrics generation works."""
    test_themes = ["sad", "happy", "love", "rock"]
    
    for theme in test_themes:
        result = generate_lyrics(theme)
        print(f"Theme: {theme}")
        print(f"Title: {result['title']}")
        print(f"Source: {result['source']}")
        print("Lyrics preview:", result['lyrics'][:200])
```

### 🚀 **Running Tests**
```bash
# Test the lyrics generation system
python Backend/Lyric_Writer.py
```

## Performance

### ⚡ **Response Times**
- **OpenRouter API**: 2-8 seconds (depends on model and complexity)
- **Fallback System**: Instant (built-in lyrics)
- **Total Timeout**: 30 seconds maximum

### 💾 **Resource Usage**
- **Memory**: Minimal footprint
- **Network**: Single API request per song
- **Storage**: No local caching (stateless)

## Security

### 🔐 **API Key Protection**
- Stored in `.env` file (not in code)
- Loaded using `dotenv_values()`
- Never logged or exposed in output

### 🛡️ **Request Safety**
- 30-second timeout prevents hanging
- Error handling prevents crashes
- Clean input validation
- No user data stored or transmitted

## Example Output

### 📝 **Sample Generated Lyrics**
```json
{
    "title": "Melancholy Dreams",
    "lyrics": "Verse 1:\nShadows dancing on the wall\nMemories of what we had\nEchoes in an empty hall\nNow my heart just feels so sad\n\nChorus:\nMelancholy dreams tonight\nFading in the pale moonlight\nLost in thoughts of yesterday\nWishing you had stayed\n\nVerse 2:\nPhotographs in dusty frames\nSmiles that used to light my way\nNow I'm calling out your name\nBut you're so far away\n\nChorus:\nMelancholy dreams tonight\nFading in the pale moonlight\nLost in thoughts of yesterday\nWishing you had stayed",
    "theme": "sad",
    "source": "openrouter"
}
```

The Lyric Writer system provides professional-quality lyrics generation with robust error handling and seamless integration with the Matrix AI singing system.
