#!/usr/bin/env python3
"""
Test script for the Enhanced Matrix AI Singing System
Tests theme-specific vocal tones and musical delivery styles
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Backend.Sing_Song import (
    THEME_VOICE_SETTINGS,
    DEFAULT_SINGING_SETTINGS,
    get_theme_voice_settings,
    enhance_lyrics_for_singing,
    generate_and_sing_song
)

def display_voice_settings():
    """Display all theme-specific voice settings"""
    print("🎵 Enhanced Matrix AI Singing System - Voice Settings")
    print("=" * 70)
    
    for theme, settings in THEME_VOICE_SETTINGS.items():
        print(f"\n🎭 {theme.upper()} Theme:")
        print(f"   Pitch: {settings['pitch']:<8} | Rate: {settings['rate']:<8} | Volume: {settings['volume']}")
        print(f"   Style: {settings['style']:<12} | Emphasis: {settings['emphasis']}")
        
        # Show what this means
        style_desc = {
            "aggressive": "Deep, powerful, commanding delivery",
            "romantic": "Soft, melodic, gentle inflection", 
            "melancholic": "Lower pitch, slow, emotional pauses",
            "energetic": "Higher pitch, upbeat, cheerful delivery",
            "sophisticated": "Smooth, rhythmic, cool delivery",
            "acoustic": "Warm, folksy, natural tone",
            "percussive": "Rhythmic, beat-focused, fast delivery",
            "refined": "Elegant, operatic, controlled delivery",
            "mainstream": "Catchy, radio-ready, commercial appeal",
            "digital": "Synthetic, robotic precision",
            "intimate": "Tender, soft, very slow delivery",
            "raw": "Fast, rebellious, aggressive attitude",
            "powerful": "Strong, inspiring, deliberate delivery",
            "island": "Laid-back, chill, relaxed pace",
            "groove": "Funky, rhythmic, strong presence"
        }
        
        desc = style_desc.get(settings['style'], "Musical delivery")
        print(f"   Description: {desc}")

def test_lyric_enhancement():
    """Test lyric enhancement for different themes"""
    print("\n🎼 Testing Lyric Enhancement by Theme")
    print("=" * 70)
    
    sample_lyrics = """Verse 1:
Walking down the street tonight
Everything feels so right
Music playing in my heart
This is just the start

Chorus:
We are alive, we are free
This is how it's meant to be
Dancing through the night
Everything's alright"""
    
    test_themes = ["rock", "love", "sad", "happy", "jazz", "rap", "classical"]
    
    for theme in test_themes:
        print(f"\n🎭 {theme.upper()} Enhancement:")
        enhanced = enhance_lyrics_for_singing(sample_lyrics, theme)
        # Show first few lines to see the enhancement
        lines = enhanced.split('\n')[:4]
        for line in lines:
            if line.strip():
                print(f"   {line}")
        print("   ...")

def test_voice_parameter_ranges():
    """Test and display the range of voice parameters"""
    print("\n🎛️ Voice Parameter Ranges")
    print("=" * 70)
    
    pitches = []
    rates = []
    volumes = []
    
    for settings in THEME_VOICE_SETTINGS.values():
        # Extract numeric values
        pitch_val = int(settings['pitch'].replace('Hz', '').replace('+', '').replace('-', ''))
        if settings['pitch'].startswith('-'):
            pitch_val = -pitch_val
            
        rate_val = int(settings['rate'].replace('%', '').replace('+', '').replace('-', ''))
        if settings['rate'].startswith('-'):
            rate_val = -rate_val
            
        volume_val = int(settings['volume'].replace('%', '').replace('+', ''))
        
        pitches.append(pitch_val)
        rates.append(rate_val)
        volumes.append(volume_val)
    
    print(f"📊 Pitch Range: {min(pitches)}Hz to +{max(pitches)}Hz")
    print(f"📊 Rate Range: {min(rates)}% to +{max(rates)}%")
    print(f"📊 Volume Range: +{min(volumes)}% to +{max(volumes)}%")
    
    print(f"\n🎯 Most Aggressive: {min(pitches)}Hz pitch, +{max(volumes)}% volume")
    print(f"🎯 Most Gentle: +{max(pitches)}Hz pitch, +{min(volumes)}% volume")
    print(f"🎯 Fastest: +{max(rates)}% rate")
    print(f"🎯 Slowest: {min(rates)}% rate")

def interactive_singing_test():
    """Interactive test for different singing themes"""
    print("\n🎤 Interactive Enhanced Singing Test")
    print("=" * 70)
    print("Test different themes with actual singing performance!")
    print("Available themes:", ", ".join(THEME_VOICE_SETTINGS.keys()))
    print("Commands:")
    print("  - 'sing [theme] song' (e.g., 'sing rock song')")
    print("  - 'test [theme]' (e.g., 'test jazz')")
    print("  - 'quit' to exit")
    print()
    
    while True:
        try:
            user_input = input("🎵 Enter command: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
            
            if user_input.startswith('test '):
                theme = user_input[5:].strip()
                if theme in THEME_VOICE_SETTINGS:
                    settings = get_theme_voice_settings(theme)
                    print(f"\n🎭 {theme.upper()} Theme Settings:")
                    print(f"   Pitch: {settings['pitch']}, Rate: {settings['rate']}, Volume: {settings['volume']}")
                    print(f"   Style: {settings['style']}, Emphasis: {settings['emphasis']}")
                    
                    # Show sample enhancement
                    sample = "I feel the music in my soul, it makes me whole"
                    enhanced = enhance_lyrics_for_singing(sample, theme)
                    print(f"   Sample: '{enhanced}'")
                else:
                    print(f"❓ Unknown theme: {theme}")
                    
            elif 'sing' in user_input.lower():
                print(f"\n🎤 Processing: '{user_input}'")
                print("   (This would perform the song with enhanced delivery)")
                print("   (Skipping actual audio generation in test mode)")
                
                # Extract theme if possible
                for theme in THEME_VOICE_SETTINGS.keys():
                    if theme in user_input.lower():
                        settings = get_theme_voice_settings(theme)
                        print(f"   🎭 Detected {theme} theme - using {settings['style']} style")
                        break
            else:
                print("❓ Try 'sing [theme] song' or 'test [theme]'")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🤖 Matrix AI Enhanced Singing System Test")
    print("🎵 Theme-Specific Vocal Tones & Musical Delivery")
    print("🎭 Authentic Singing Voices for Each Genre")
    print("=" * 70)
    
    # Display all voice settings
    display_voice_settings()
    
    # Test lyric enhancement
    test_lyric_enhancement()
    
    # Show parameter ranges
    test_voice_parameter_ranges()
    
    # Ask for interactive test
    print("\n" + "=" * 70)
    response = input("🎮 Run interactive singing test? (y/n): ").strip().lower()
    if response in ['y', 'yes']:
        interactive_singing_test()
    
    print("\n✅ Enhanced singing system test completed!")
    print("🎯 The system now supports authentic musical delivery for each theme!")
    print("🎵 Each genre has unique pitch, rate, volume, and emphasis settings!")

if __name__ == "__main__":
    main()
